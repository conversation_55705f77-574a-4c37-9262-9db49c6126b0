# Split 和 Settle 測試數據

## Split 測試消息

### 測試消息 1: 餐廳分帳
```json
{
  "content": "Dinner at Italian Restaurant",
  "type": "split",
  "metadata": {
    "messageId": "test-split-msg-001",
    "title": "Dinner at Italian Restaurant",
    "description": "Team dinner after project completion",
    "category": "food",
    "totalAmount": 2400.0,
    "currency": "NT$",
    "paidByUserId": "user-001",
    "paidByUserName": "Alice Chen",
    "splitType": "equal",
    "participants": [
      {
        "userId": "user-001",
        "userName": "Alice Chen",
        "amount": 600.0
      },
      {
        "userId": "user-002", 
        "userName": "<PERSON> Wang",
        "amount": 600.0
      },
      {
        "userId": "user-003",
        "userName": "<PERSON>",
        "amount": 600.0
      },
      {
        "userId": "user-004",
        "userName": "<PERSON>",
        "amount": 600.0
      }
    ],
    "creatorId": "user-001",
    "createdAt": "2025-01-27T10:30:00Z",
    "isDeleted": false
  }
}
```

### 測試消息 2: 交通費分帳
```json
{
  "content": "Taxi to Airport",
  "type": "split", 
  "metadata": {
    "messageId": "test-split-msg-002",
    "title": "Taxi to Airport",
    "description": "Shared taxi ride for business trip",
    "category": "transport",
    "totalAmount": 800.0,
    "currency": "NT$",
    "paidByUserId": "user-002",
    "paidByUserName": "Bob Wang",
    "splitType": "equal",
    "participants": [
      {
        "userId": "user-002",
        "userName": "Bob Wang", 
        "amount": 400.0
      },
      {
        "userId": "user-003",
        "userName": "Carol Liu",
        "amount": 400.0
      }
    ],
    "creatorId": "user-002",
    "createdAt": "2025-01-27T11:15:00Z",
    "isDeleted": false
  }
}
```

## Settle 測試消息

### 測試消息 1: 還款給 Alice
```json
{
  "content": "Payment to Alice for dinner",
  "type": "settle",
  "metadata": {
    "fromUserId": "user-002",
    "fromUserName": "Bob Wang",
    "toUserId": "user-001", 
    "toUserName": "Alice Chen",
    "amount": 600.0,
    "currency": "NT$",
    "description": "Repayment for Italian restaurant dinner",
    "createdAt": "2025-01-27T12:00:00Z"
  }
}
```

### 測試消息 2: 還款給 Bob
```json
{
  "content": "Payment to Bob for taxi",
  "type": "settle",
  "metadata": {
    "fromUserId": "user-003",
    "fromUserName": "Carol Liu", 
    "toUserId": "user-002",
    "toUserName": "Bob Wang",
    "amount": 400.0,
    "currency": "NT$",
    "description": "Repayment for airport taxi",
    "createdAt": "2025-01-27T12:30:00Z"
  }
}
```

## 測試步驟

### Split 刪除狀態測試
1. 在聊天中發送 Split 測試消息 1 或 2
2. 前往 Balance 頁面的 Split 標籤
3. 刪除該 Split 記錄
4. 返回聊天頁面
5. 驗證該 Split 消息顯示專用的刪除狀態UI：
   - 灰色背景和邊框
   - "Split" 標籤和 "Deleted" 徽章
   - 劃線的標題文字
   - "This expense has been deleted" 提示文字

### Settle 刪除狀態測試
1. 在聊天中發送 Settle 測試消息 1 或 2
2. 前往 Balance 頁面的 Settle 標籤
3. 刪除該 Settle 記錄
4. 返回聊天頁面
5. 驗證該 Settle 消息顯示專用的刪除狀態UI：
   - 灰色背景和邊框
   - "Settle" 標籤和 "Deleted" 徽章
   - 劃線的付款人信息
   - "This payment has been deleted" 提示文字

## 預期結果

- Split 和 Settle 消息在被從其他標籤刪除後，應該顯示專用的刪除狀態UI
- 不應該顯示通用的 "message has been deleted" 文字
- 刪除狀態UI 應該與 ToGo 的刪除狀態保持一致的設計風格
- 用戶可以清楚識別這是一個已刪除的 Split/Settle 項目
