import { IsS<PERSON>, <PERSON><PERSON>ptional, <PERSON><PERSON><PERSON>y, IsDateString, <PERSON><PERSON>ength, <PERSON>E<PERSON> } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { TodoParticipantRole } from '../../../entities/todo.entity';

export class CreateTodoParticipantDto {
  @ApiProperty({
    description: 'User ID of the participant',
    example: 'uuid-of-user',
  })
  @IsString()
  userId: string;

  @ApiProperty({
    description: 'Display name of the participant',
    example: '<PERSON>',
  })
  @IsString()
  @MaxLength(100)
  userName: string;

  @ApiPropertyOptional({
    description: 'Role of the participant in the todo',
    enum: TodoParticipantRole,
    default: TodoParticipantRole.ASSIGNEE,
  })
  @IsOptional()
  @IsEnum(TodoParticipantRole)
  role?: TodoParticipantRole = TodoParticipantRole.ASSIGNEE;

  @ApiPropertyOptional({
    description: 'Whether the participant should be notified',
    default: true,
  })
  @IsOptional()
  isNotified?: boolean = true;
}

export class CreateTodoDto {
  @ApiProperty({
    description: 'Todo title',
    example: 'Buy groceries',
    maxLength: 200,
  })
  @IsString()
  @MaxLength(200)
  title: string;

  @ApiPropertyOptional({
    description: 'Todo details/description',
    example: 'Need to buy milk, bread, and eggs from the supermarket',
  })
  @IsOptional()
  @IsString()
  details?: string;

  @ApiPropertyOptional({
    description: 'Due date for the todo (date only)',
    example: '2025-01-25',
  })
  @IsOptional()
  @IsDateString()
  dueDate?: string;

  @ApiPropertyOptional({
    description: 'Due time for the todo (time only)',
    example: '18:00',
  })
  @IsOptional()
  dueTime?: string | null;

  @ApiPropertyOptional({
    description: 'Location related to the todo',
    example: 'Supermarket on Main Street',
  })
  @IsOptional()
  @IsString()
  location?: string;

  @ApiPropertyOptional({
    description: 'List of tag names for the todo',
    example: ['work', 'urgent'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({
    description: 'List of participants for the todo',
    type: [CreateTodoParticipantDto],
  })
  @IsOptional()
  @IsArray()
  participants?: CreateTodoParticipantDto[];

  @ApiPropertyOptional({
    description: 'Message ID if this todo is created from a chat message',
    example: 'uuid-of-message',
  })
  @IsOptional()
  @IsString()
  messageId?: string;
}
