import { Injectable, NotFoundException, ForbiddenException, BadRequestException, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindManyOptions, Like, In, <PERSON>Than, <PERSON>Than } from 'typeorm';
import { Todo, TodoStatus } from '../../entities/todo.entity';


import { Tag } from '../../entities/tag.entity';
import { SpaceMember } from '../../entities/space-member.entity';
import { Message, MessageType } from '../../entities/message.entity';
import { User } from '../../entities/user.entity';
import { CreateTodoDto } from './dto/create-todo.dto';
import { UpdateTodoDto, CompleteTodoDto } from './dto/update-todo.dto';
import { GetTodosDto } from './dto/get-todos.dto';
import { ChatGateway } from '../chat/chat.gateway';
import { TagService } from '../tag/tag.service';


@Injectable()
export class TodoService {
  constructor(
    @InjectRepository(Todo)
    private todoRepository: Repository<Todo>,


    @InjectRepository(SpaceMember)
    private spaceMemberRepository: Repository<SpaceMember>,
    @InjectRepository(Message)
    private messageRepository: Repository<Message>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Tag)
    private tagRepository: Repository<Tag>,
    @Inject(forwardRef(() => ChatGateway))
    private chatGateway: ChatGateway,
    @Inject(forwardRef(() => TagService))
    private tagService: TagService,

  ) {}

  async createTodo(
    spaceId: string,
    userId: string,
    createTodoDto: CreateTodoDto,
  ): Promise<Todo> {
    // Check if user is a member of the space
    await this.checkSpaceMembership(spaceId, userId);

    // Determine initial status based on due date
    const initialStatus = createTodoDto.dueDate ? TodoStatus.IN_PROGRESS : TodoStatus.PENDING;

    // Create a chat message for this TODO if not created from a message
    let messageId = createTodoDto.messageId;
    if (!messageId) {
      // Create TODO metadata for the message
      const todoMetadata = {
        title: createTodoDto.title,
        details: createTodoDto.details,
        dueDate: createTodoDto.dueDate,
        dueTime: createTodoDto.dueTime,
        location: createTodoDto.location,
        // Note: Tags are now managed through the unified Tag system
        participants: createTodoDto.participants || [],
        creatorId: userId,
      };

      // Create the message
      const message = this.messageRepository.create({
        spaceId,
        senderId: userId,
        type: MessageType.TODO,
        content: createTodoDto.title,
        metadata: todoMetadata,
      });

      const savedMessage = await this.messageRepository.save(message);
      messageId = savedMessage.id;
    }

    // Create the todo
    const todo = this.todoRepository.create({
      spaceId,
      creatorId: userId,
      messageId: messageId,
      title: createTodoDto.title,
      details: createTodoDto.details,
      dueDate: createTodoDto.dueDate ? new Date(createTodoDto.dueDate) : undefined,
      dueTime: createTodoDto.dueTime,
      location: createTodoDto.location,
      status: initialStatus,
    });

    const savedTodo = await this.todoRepository.save(todo);

    // Handle tags if provided - store tag IDs directly in todos table
    if (createTodoDto.tags && createTodoDto.tags.length > 0) {
      try {
        const tags = await this.tagService.getOrCreateTags(
          spaceId,
          userId,
          createTodoDto.tags,
        );

        // Store tag IDs as JSON array in the todos table
        const tagIds = tags.map(tag => tag.id);
        savedTodo.tagIds = JSON.stringify(tagIds);
        await this.todoRepository.save(savedTodo);
      } catch (error) {
        console.error('❌ Error processing tags:', error);
        throw error;
      }
    }

    // Handle participants if provided - store participant data directly in todos table
    if (createTodoDto.participants && createTodoDto.participants.length > 0) {
      const participantsData = [];

      for (const participantData of createTodoDto.participants) {
        // Get user information to populate userName if not provided
        const user = await this.userRepository.findOne({
          where: { id: participantData.userId },
        });

        if (user) {
          participantsData.push({
            userId: participantData.userId,
            userName: participantData.userName || user.displayName || `User ${participantData.userId}`,
            role: participantData.role || 'assignee',
            isNotified: participantData.isNotified !== undefined ? participantData.isNotified : true,
          });
        }
      }

      if (participantsData.length > 0) {
        savedTodo.participantData = JSON.stringify(participantsData);
        await this.todoRepository.save(savedTodo);
      }
    }

    // Return the todo with relations
    const todoWithRelations = await this.todoRepository.findOne({
      where: { id: savedTodo.id },
      relations: ['creator', 'completedByUser'],
    });

    // Load tags for this todo
    const [todoWithTags] = await this.loadTagsForTodos([todoWithRelations]);

    // Add participants data as virtual property
    (todoWithTags as any).participants = todoWithTags.participantsArray;

    // If we created a new message, broadcast it to the chat
    if (!createTodoDto.messageId) {
      const messageWithSender = await this.messageRepository.findOne({
        where: { id: messageId },
        relations: ['sender'],
      });

      if (messageWithSender) {
        // Broadcast the new message to the chat
        this.chatGateway.server.to(`space:${spaceId}`).emit('new-message', {
          spaceId,
          message: messageWithSender,
        });
      }
    }

    // Broadcast todo creation via WebSocket
    this.chatGateway.broadcastTodoCreated(spaceId, todoWithRelations);

    return todoWithRelations;
  }

  // Note: Space tags are now managed through the unified Tag system
  // Use the Tag API endpoints instead

  async getTodos(
    spaceId: string,
    userId: string,
    getTodosDto: GetTodosDto,
  ): Promise<{ todos: Todo[]; total: number }> {
    // Check if user is a member of the space
    await this.checkSpaceMembership(spaceId, userId);

    const {
      limit,
      offset,
      status,
      tags,
      dueBefore,
      dueAfter,
      createdBefore,
      createdAfter,
      creatorId,
      participantId,
      search,
      sortBy,
      sortOrder,
    } = getTodosDto;

    const whereConditions: any = { spaceId };

    // Filter by status
    if (status) {
      const statusArray = status.split(',').map(s => s.trim());
      whereConditions.status = In(statusArray);
    }

    // Filter by due date
    if (dueBefore) {
      whereConditions.dueDate = LessThan(new Date(dueBefore));
    }
    if (dueAfter) {
      whereConditions.dueDate = MoreThan(new Date(dueAfter));
    }

    // Filter by creation date
    if (createdBefore) {
      whereConditions.createdAt = LessThan(new Date(createdBefore));
    }
    if (createdAfter) {
      whereConditions.createdAt = MoreThan(new Date(createdAfter));
    }

    // Filter by creator
    if (creatorId) {
      whereConditions.creatorId = creatorId;
    }

    // Search in title and details
    if (search) {
      whereConditions.title = Like(`%${search}%`);
    }

    const findOptions: FindManyOptions<Todo> = {
      where: whereConditions,
      relations: ['creator', 'completedByUser'],
      order: { [sortBy]: sortOrder },
      take: limit,
      skip: offset,
    };

    let [todos, total] = await this.todoRepository.findAndCount(findOptions);

    // Load tags for all todos
    todos = await this.loadTagsForTodos(todos);

    // Add participants data as virtual property for each todo
    todos.forEach(todo => {
      (todo as any).participants = todo.participantsArray;
    });

    // Filter by tags if specified
    if (tags) {
      const tagArray = tags.split(',').map(t => t.trim());

      // Filter todos that have any of the specified tags
      todos = todos.filter(todo => {
        const todoTags = (todo as any).tags || [];
        return todoTags.some((tag: any) => tagArray.includes(tag.name));
      });
      total = todos.length;
    }

    // Filter by participant if specified
    if (participantId) {
      todos = todos.filter(todo => {
        const participants = todo.participantsArray || [];
        return participants.some((p: any) => p.userId === participantId);
      });
      total = todos.length;
    }

    return { todos, total };
  }

  async getTodoById(
    todoId: string,
    userId: string,
  ): Promise<Todo> {
    const todo = await this.todoRepository.findOne({
      where: { id: todoId },
      relations: ['creator', 'completedByUser', 'space'],
    });

    if (!todo) {
      throw new NotFoundException('Todo not found');
    }

    // Check if user is a member of the space
    await this.checkSpaceMembership(todo.spaceId, userId);

    // Load tags for this todo
    const [todoWithTags] = await this.loadTagsForTodos([todo]);

    // Add participants data as virtual property
    (todoWithTags as any).participants = todoWithTags.participantsArray;

    return todoWithTags;
  }

  async updateTodo(
    todoId: string,
    userId: string,
    updateTodoDto: UpdateTodoDto,
  ): Promise<Todo> {
    const todo = await this.getTodoById(todoId, userId);

    // Check if user can update this todo (creator or participant)
    const participants = todo.participantsArray || [];
    const canUpdate = todo.creatorId === userId ||
      participants.some((p: any) => p.userId === userId);

    if (!canUpdate) {
      throw new ForbiddenException('You can only update todos you created or are assigned to');
    }

    // Update basic fields
    if (updateTodoDto.title !== undefined) {
      todo.title = updateTodoDto.title;
    }
    if (updateTodoDto.details !== undefined) {
      todo.details = updateTodoDto.details;
    }

    // Handle status updates - allow all status changes
    if (updateTodoDto.status !== undefined) {
      todo.status = updateTodoDto.status;

      // If marking as completed, set completion info
      if (updateTodoDto.status === TodoStatus.COMPLETED) {
        todo.completedAt = new Date();
        todo.completedByUserId = userId;
      } else {
        // Clear completion info for other statuses
        todo.completedAt = null;
        todo.completedByUserId = null;
      }
    }

    // Handle due date updates and automatic status changes
    if (updateTodoDto.dueDate !== undefined) {
      todo.dueDate = updateTodoDto.dueDate ? new Date(updateTodoDto.dueDate) : null;

      // Auto-update status based on due date presence (only if status wasn't manually set)
      if (updateTodoDto.status === undefined) {
        if (todo.dueDate) {
          // Has due date - set to in_progress
          todo.status = TodoStatus.IN_PROGRESS;
        } else {
          // No due date - set to pending
          todo.status = TodoStatus.PENDING;
        }
      }
    }

    // Handle due time updates
    if (updateTodoDto.dueTime !== undefined) {
      todo.dueTime = updateTodoDto.dueTime;
    }
    if (updateTodoDto.location !== undefined) {
      todo.location = updateTodoDto.location;
    }
    // Note: Tags are now managed through the unified Tag system

    // Update participants if provided - store participant data directly in todos table
    if (updateTodoDto.participants !== undefined) {
      try {
        console.log(`👥 Updating participants for TODO: ${todoId}, participants: ${JSON.stringify(updateTodoDto.participants)}`);

        const participantsData = [];

        // Filter out duplicates by userId from incoming data
        const uniqueParticipants = updateTodoDto.participants.filter((participant, index, self) =>
          self.findIndex(p => p.userId === participant.userId) === index
        );

        for (const participantData of uniqueParticipants) {
          // Get user information to populate userName if not provided
          const user = await this.userRepository.findOne({
            where: { id: participantData.userId },
          });

          if (user) {
            participantsData.push({
              userId: participantData.userId,
              userName: participantData.userName || user.displayName || `User ${participantData.userId}`,
              role: participantData.role || 'assignee',
              isNotified: participantData.isNotified !== undefined ? participantData.isNotified : true,
            });
          }
        }

        // Store participant data as JSON array directly in the todos table
        todo.participantData = participantsData.length > 0 ? JSON.stringify(participantsData) : null;

        console.log(`✅ Successfully updated participants for TODO: ${todoId}`);
      } catch (error) {
        console.error('❌ Error updating participants:', error);
        throw error;
      }
    }

    // Handle tags update if provided - store tag IDs directly in todos table
    if (updateTodoDto.tags !== undefined) {
      try {
        console.log(`🏷️ Updating tags for TODO: ${todoId}, tags: ${JSON.stringify(updateTodoDto.tags)}`);

        // Get or create tags
        const tags = updateTodoDto.tags && updateTodoDto.tags.length > 0
          ? await this.tagService.getOrCreateTags(todo.spaceId, userId, updateTodoDto.tags)
          : [];

        console.log(`🏷️ Target tags: ${JSON.stringify(tags.map(t => ({ id: t.id, name: t.name })))}`);

        // Store tag IDs as JSON array directly in the todos table
        const tagIds = tags.map(t => t.id);
        todo.tagIds = tagIds.length > 0 ? JSON.stringify(tagIds) : null;

        console.log(`✅ Successfully updated tags for TODO: ${todoId}`);
      } catch (error) {
        console.error('❌ Error updating tags:', error);
        throw error;
      }
    }

    await this.todoRepository.save(todo);

    // Return updated todo with relations
    return this.getTodoById(todoId, userId);
  }

  async completeTodo(
    todoId: string,
    userId: string,
    completeTodoDto?: CompleteTodoDto,
  ): Promise<Todo> {
    const todo = await this.getTodoById(todoId, userId);

    // Check if user can complete this todo (creator or participant)
    const participants = todo.participantsArray || [];
    const canComplete = todo.creatorId === userId ||
      participants.some((p: any) => p.userId === userId);

    if (!canComplete) {
      throw new ForbiddenException('You can only complete todos you created or are assigned to');
    }

    if (todo.status === TodoStatus.COMPLETED) {
      throw new ForbiddenException('Todo is already completed');
    }

    todo.status = TodoStatus.COMPLETED;
    todo.completedAt = new Date();
    todo.completedByUserId = userId;

    await this.todoRepository.save(todo);

    return this.getTodoById(todoId, userId);
  }

  async deleteAllTodos(spaceId: string, userId: string): Promise<{ deletedTodos: number; deletedParticipants: number }> {
    // Check space membership
    await this.checkSpaceMembership(spaceId, userId);

    // Get all todos in the space
    const todos = await this.todoRepository.find({
      where: { spaceId },
    });

    const todoIds = todos.map(todo => todo.id);

    // Decrement usage count for all associated tags before deletion
    for (const todo of todos) {
      if (todo.tagIds) {
        try {
          const tagIds = JSON.parse(todo.tagIds);
          for (const tagId of tagIds) {
            await this.tagService.decrementUsageCount(tagId);
          }
        } catch (error) {
          console.error('❌ Error decrementing tag usage count for todo:', todo.id, error);
          // Continue with deletion even if tag count update fails
        }
      }
    }

    // Participants are now stored directly in todos table, no separate deletion needed
    let deletedParticipants = 0;

    // Delete all todos
    const todosResult = await this.todoRepository.delete({
      spaceId,
    });
    const deletedTodos = todosResult.affected || 0;

    console.log(`🗑️ Deleted ${deletedTodos} TODOs and ${deletedParticipants} participants from space ${spaceId}`);

    return {
      deletedTodos,
      deletedParticipants,
    };
  }

  async deleteAllTodosGlobally(userId: string): Promise<{ deletedTodos: number; deletedParticipants: number }> {
    // Get all todos first to handle tag counts
    const todos = await this.todoRepository.find();

    // Decrement usage count for all associated tags before deletion
    for (const todo of todos) {
      if (todo.tagIds) {
        try {
          const tagIds = JSON.parse(todo.tagIds);
          for (const tagId of tagIds) {
            await this.tagService.decrementUsageCount(tagId);
          }
        } catch (error) {
          console.error('❌ Error decrementing tag usage count for todo:', todo.id, error);
          // Continue with deletion even if tag count update fails
        }
      }
    }

    // Participants are now stored directly in todos table, no separate deletion needed
    const deletedParticipants = 0;

    // Delete all todos
    const todosResult = await this.todoRepository
      .createQueryBuilder()
      .delete()
      .execute();
    const deletedTodos = todosResult.affected || 0;

    return {
      deletedTodos,
      deletedParticipants,
    };
  }

  async syncTodoFromMessage(
    spaceId: string,
    messageId: string,
    userId: string,
  ): Promise<Todo> {
    // Check space membership
    await this.checkSpaceMembership(spaceId, userId);

    // Check if todo already exists
    const existingTodo = await this.todoRepository.findOne({
      where: { messageId, spaceId },
    });

    if (existingTodo) {
      return existingTodo;
    }

    // Get the message to extract metadata
    const message = await this.messageRepository.findOne({
      where: { id: messageId, spaceId },
    });

    if (!message || message.type !== 'todo') {
      throw new NotFoundException('TODO message not found');
    }

    const todoData = message.metadata as any;
    if (!todoData) {
      throw new BadRequestException('Message has no TODO metadata');
    }

    // Determine initial status based on due date
    const initialStatus = todoData.dueDate ? TodoStatus.IN_PROGRESS : TodoStatus.PENDING;

    // Create the todo record
    const todo = this.todoRepository.create({
      spaceId,
      messageId: message.id,
      creatorId: message.senderId,
      title: todoData.title || message.content,
      details: todoData.details,
      dueDate: todoData.dueDate ? new Date(todoData.dueDate) : null,
      location: todoData.location,
      status: initialStatus,
    });

    const savedTodo = await this.todoRepository.save(todo);

    // Create participants if provided
    if (todoData.participants && todoData.participants.length > 0) {
      const participantsData = [];

      for (const participantData of todoData.participants) {
        const user = await this.userRepository.findOne({
          where: { id: participantData.userId },
        });

        if (user) {
          participantsData.push({
            userId: user.id,
            userName: user.displayName,
            role: 'assignee',
            isNotified: true,
          });
        }
      }

      if (participantsData.length > 0) {
        savedTodo.participantData = JSON.stringify(participantsData);
        await this.todoRepository.save(savedTodo);
      }
    }

    // Return the todo with virtual participants and tags
    const finalTodo = await this.todoRepository.findOne({
      where: { id: savedTodo.id },
    });

    if (finalTodo) {
      (finalTodo as any).participants = finalTodo.participantsArray;
      (finalTodo as any).tags = finalTodo.tagIdsArray;
    }

    return finalTodo;
  }



  async updateTodoStatusByMessageId(
    spaceId: string,
    messageId: string,
    userId: string,
    status: TodoStatus,
  ): Promise<Todo> {
    // Check if user is a member of the space
    await this.checkSpaceMembership(spaceId, userId);

    // Find todo by message ID
    const todo = await this.todoRepository.findOne({
      where: { messageId, spaceId },
      relations: ['creator', 'completedByUser'],
    });

    if (!todo) {
      throw new NotFoundException('Todo not found for this message');
    }

    // Check if user can update this todo (creator or participant)
    const participants = todo.participantsArray || [];
    const canUpdate = todo.creatorId === userId ||
      participants.some((p: any) => p.userId === userId);

    if (!canUpdate) {
      throw new ForbiddenException('You can only update todos you created or are assigned to');
    }

    // Update status
    todo.status = status;
    if (status === TodoStatus.COMPLETED) {
      todo.completedAt = new Date();
      todo.completedByUserId = userId;
    } else {
      todo.completedAt = null;
      todo.completedByUserId = null;
    }

    await this.todoRepository.save(todo);

    // Update the message metadata to reflect the new status
    await this.updateMessageMetadata(messageId, todo);

    // Broadcast todo status change via WebSocket
    this.chatGateway.broadcastTodoStatusChanged(spaceId, todo);

    return todo;
  }

  async deleteTodo(
    todoId: string,
    userId: string,
  ): Promise<void> {
    const todo = await this.getTodoById(todoId, userId);

    // Only creator can delete the todo
    if (todo.creatorId !== userId) {
      throw new ForbiddenException('You can only delete todos you created');
    }

    // Decrement usage count for associated tags before deletion
    if (todo.tagIds) {
      try {
        const tagIds = JSON.parse(todo.tagIds);
        for (const tagId of tagIds) {
          await this.tagService.decrementUsageCount(tagId);
        }
      } catch (error) {
        console.error('❌ Error decrementing tag usage count:', error);
        // Continue with deletion even if tag count update fails
      }
    }

    // Soft delete the todo
    await this.todoRepository.softDelete(todoId);

    // If there's an associated message, soft delete it too to maintain data consistency
    if (todo.messageId) {
      await this.messageRepository.softDelete(todo.messageId);
    }
  }

  private async updateMessageMetadata(messageId: string, todo: Todo): Promise<void> {
    try {
      const message = await this.messageRepository.findOne({
        where: { id: messageId },
      });

      if (message && message.metadata) {
        // Update the metadata with the new todo status
        const updatedMetadata = {
          ...message.metadata,
          status: todo.status,
          completedAt: todo.completedAt?.toISOString() || null,
          completedByUserId: todo.completedByUserId || null,
        };

        message.metadata = updatedMetadata;
        await this.messageRepository.save(message);
      }
    } catch (error) {
      // Log error but don't throw - message metadata update is not critical
      console.error('Failed to update message metadata:', error);
    }
  }

  private async checkSpaceMembership(spaceId: string, userId: string): Promise<void> {
    const membership = await this.spaceMemberRepository.findOne({
      where: { spaceId, userId },
    });

    if (!membership) {
      throw new ForbiddenException('You are not a member of this space');
    }
  }

  private generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * Load tags for todos based on their tagIds field
   */
  private async loadTagsForTodos(todos: Todo[]): Promise<Todo[]> {
    if (!todos.length) return todos;

    // Collect all unique tag IDs
    const allTagIds = new Set<string>();
    todos.forEach(todo => {
      if (todo.tagIds) {
        try {
          const tagIds = JSON.parse(todo.tagIds);
          tagIds.forEach((id: string) => allTagIds.add(id));
        } catch {
          // Ignore invalid JSON
        }
      }
    });

    if (allTagIds.size === 0) return todos;

    // Load all tags in one query
    const tags = await this.tagRepository.find({
      where: { id: In(Array.from(allTagIds)) }
    });
    const tagMap = new Map(tags.map(tag => [tag.id, tag]));

    // Attach tags to todos
    return todos.map(todo => {
      if (todo.tagIds) {
        try {
          const tagIds = JSON.parse(todo.tagIds);
          (todo as any).tags = tagIds
            .map((id: string) => tagMap.get(id))
            .filter(Boolean);
        } catch {
          (todo as any).tags = [];
        }
      } else {
        (todo as any).tags = [];
      }
      return todo;
    });
  }
}