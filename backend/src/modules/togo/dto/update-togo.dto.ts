import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  MaxLength,
  IsArray,
  IsEnum,
  IsNumber,
  IsDateString,
  Min,
  Max,
} from 'class-validator';


export class UpdateToGoDto {
  @ApiPropertyOptional({
    description: 'Place name',
    example: 'Tokyo Tower',
    maxLength: 200,
  })
  @IsOptional()
  @IsString()
  @MaxLength(200)
  name?: string;

  @ApiPropertyOptional({
    description: 'Place description',
    example: 'Famous landmark in Tokyo with great city views',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Address of the place',
    example: '4 Chome-2-8 Shibakoen, Minato City, Tokyo 105-0011, Japan',
  })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiPropertyOptional({
    description: 'Latitude coordinate',
    example: 35.6586,
    minimum: -90,
    maximum: 90,
  })
  @IsOptional()
  @IsNumber()
  @Min(-90)
  @Max(90)
  latitude?: number;

  @ApiPropertyOptional({
    description: 'Longitude coordinate',
    example: 139.7454,
    minimum: -180,
    maximum: 180,
  })
  @IsOptional()
  @IsNumber()
  @Min(-180)
  @Max(180)
  longitude?: number;

  @ApiPropertyOptional({
    description: 'Original link or URL reference',
    example: 'https://example.com/place',
  })
  @IsOptional()
  @IsString()
  originalLink?: string;

  @ApiPropertyOptional({
    description: 'List of tag names for this place',
    example: ['travel', 'restaurant'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];
}
