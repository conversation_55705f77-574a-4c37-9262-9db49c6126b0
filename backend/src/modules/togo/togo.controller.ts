import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ToGoService } from './togo.service';
import { CreateToGoDto } from './dto/create-togo.dto';
import { UpdateToGoDto } from './dto/update-togo.dto';
import { ApiResponseDto } from '../../common/dto/api-response.dto';

@ApiTags('ToGo Places')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('spaces/:spaceId/togos')
export class ToGoController {
  constructor(private readonly togoService: ToGoService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new ToGo place' })
  @ApiParam({ name: 'spaceId', description: 'Space ID' })
  @ApiResponse({
    status: 201,
    description: 'ToGo place created successfully',
  })
  @ApiResponse({
    status: 403,
    description: 'User is not a member of the space',
  })
  @ApiResponse({
    status: 404,
    description: 'Space not found',
  })
  async create(
    @Param('spaceId', ParseUUIDPipe) spaceId: string,
    @Body() createToGoDto: CreateToGoDto,
    @Request() req: any,
  ) {
    return this.togoService.create(spaceId, req.user.id, createToGoDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all ToGo places in a space' })
  @ApiParam({ name: 'spaceId', description: 'Space ID' })
  @ApiResponse({
    status: 200,
    description: 'ToGo places retrieved successfully',
  })
  @ApiResponse({
    status: 403,
    description: 'User is not a member of the space',
  })
  @ApiResponse({
    status: 404,
    description: 'Space not found',
  })
  async findAll(@Param('spaceId', ParseUUIDPipe) spaceId: string, @Request() req: any) {
    return this.togoService.findBySpace(spaceId, req.user.id);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific ToGo place' })
  @ApiParam({ name: 'spaceId', description: 'Space ID' })
  @ApiParam({ name: 'id', description: 'ToGo place ID' })
  @ApiResponse({
    status: 200,
    description: 'ToGo place retrieved successfully',
  })
  @ApiResponse({
    status: 403,
    description: 'User is not a member of the space',
  })
  @ApiResponse({
    status: 404,
    description: 'ToGo place not found',
  })
  async findOne(
    @Param('spaceId', ParseUUIDPipe) spaceId: string,
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ) {
    return this.togoService.findOne(id, req.user.id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a ToGo place' })
  @ApiParam({ name: 'spaceId', description: 'Space ID' })
  @ApiParam({ name: 'id', description: 'ToGo place ID' })
  @ApiResponse({
    status: 200,
    description: 'ToGo place updated successfully',
  })
  @ApiResponse({
    status: 403,
    description: 'Only the creator can update this place',
  })
  @ApiResponse({
    status: 404,
    description: 'ToGo place not found',
  })
  async update(
    @Param('spaceId', ParseUUIDPipe) spaceId: string,
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateToGoDto: UpdateToGoDto,
    @Request() req: any,
  ) {
    return this.togoService.update(id, req.user.id, updateToGoDto);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete a ToGo place' })
  @ApiParam({ name: 'spaceId', description: 'Space ID' })
  @ApiParam({ name: 'id', description: 'ToGo place ID' })
  @ApiResponse({
    status: 204,
    description: 'ToGo place deleted successfully',
  })
  @ApiResponse({
    status: 403,
    description: 'Only the creator can delete this place',
  })
  @ApiResponse({
    status: 404,
    description: 'ToGo place not found',
  })
  async remove(
    @Param('spaceId') spaceId: string,
    @Param('id') id: string,
    @Request() req: any,
  ) {
    return this.togoService.remove(id, req.user.id);
  }



  // Note: Space tags are now managed through the unified Tag system
  // Use the Tag API endpoints instead of this deprecated endpoint
}
