import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
  Inject,
  forwardRef,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { ToGo } from '../../entities/togo.entity';
import { Message, MessageType } from '../../entities/message.entity';
import { User } from '../../entities/user.entity';
import { Tag } from '../../entities/tag.entity';
import { Space, SpaceMember } from '../../entities';
import { CreateToGoDto } from './dto/create-togo.dto';
import { UpdateToGoDto } from './dto/update-togo.dto';
import { ChatGateway } from '../chat/chat.gateway';
import { TagService } from '../tag/tag.service';


@Injectable()
export class ToGoService {
  constructor(
    @InjectRepository(ToGo)
    private togoRepository: Repository<ToGo>,
    @InjectRepository(Message)
    private messageRepository: Repository<Message>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Tag)
    private tagRepository: Repository<Tag>,
    @InjectRepository(Space)
    private spaceRepository: Repository<Space>,
    @InjectRepository(SpaceMember)
    private spaceMemberRepository: Repository<SpaceMember>,
    @Inject(forwardRef(() => ChatGateway))
    private chatGateway: ChatGateway,
    private tagService: TagService,
  ) {}

  async create(
    spaceId: string,
    userId: string,
    createToGoDto: CreateToGoDto,
  ): Promise<ToGo> {
    // Verify space exists and user is a member
    await this.verifySpaceAccess(spaceId, userId);

    // Create a chat message for this ToGo
    const togoMetadata = {
      name: createToGoDto.name,
      description: createToGoDto.description,
      address: createToGoDto.address,
      latitude: createToGoDto.latitude,
      longitude: createToGoDto.longitude,
      originalLink: createToGoDto.originalLink,
      creatorId: userId,
    };

    // Create the message first
    const message = this.messageRepository.create({
      spaceId,
      senderId: userId,
      type: MessageType.TOGO,
      content: `Shared a place: ${createToGoDto.name}`,
      metadata: togoMetadata,
    });

    const savedMessage = await this.messageRepository.save(message);

    // Create the ToGo record with message reference
    const togo = this.togoRepository.create({
      ...createToGoDto,
      spaceId,
      creatorId: userId,
      messageId: savedMessage.id,
    });

    const savedTogo = await this.togoRepository.save(togo);

    // Handle tags if provided - store tag IDs directly in togos table
    if (createToGoDto.tags && createToGoDto.tags.length > 0) {
      try {
        const tags = await this.tagService.getOrCreateTags(
          spaceId,
          userId,
          createToGoDto.tags,
        );

        // Store tag IDs as JSON array in the togos table
        const tagIds = tags.map(tag => tag.id);
        savedTogo.tagIds = JSON.stringify(tagIds);
        await this.togoRepository.save(savedTogo);
      } catch (error) {
        console.error('❌ Error processing tags:', error);
        throw error;
      }
    }

    // Get the complete ToGo with relations
    const togoWithRelations = await this.togoRepository.findOne({
      where: { id: savedTogo.id },
      relations: ['creator'],
      select: [
        'id',
        'spaceId',
        'messageId',
        'creatorId',
        'name',
        'description',
        'address',
        'latitude',
        'longitude',
        'originalLink',
        'tagIds',
        'createdAt',
        'updatedAt',
        'deletedAt',
      ],
    });

    // Get the message with sender information for broadcasting
    const messageWithSender = await this.messageRepository.findOne({
      where: { id: savedMessage.id },
      relations: ['sender'],
    });

    if (messageWithSender) {
      // Broadcast the new message to the chat
      this.chatGateway.server.to(`space:${spaceId}`).emit('new-message', {
        spaceId,
        message: messageWithSender,
      });
    }

    return togoWithRelations;
  }

  async findBySpace(spaceId: string, userId: string): Promise<ToGo[]> {
    // Verify space access
    await this.verifySpaceAccess(spaceId, userId);

    let togos = await this.togoRepository.find({
      where: { spaceId },
      relations: ['creator'],
      order: { createdAt: 'DESC' },
      select: [
        'id',
        'spaceId',
        'messageId',
        'creatorId',
        'name',
        'description',
        'address',
        'latitude',
        'longitude',
        'originalLink',
        'tagIds',
        'createdAt',
        'updatedAt',
        'deletedAt',
      ],
    });

    // Load tags for all togos
    togos = await this.loadTagsForTogos(togos);

    return togos;
  }

  async findOne(id: string, userId: string): Promise<ToGo> {
    const togo = await this.togoRepository.findOne({
      where: { id },
      relations: ['creator', 'space'],
      select: [
        'id',
        'spaceId',
        'messageId',
        'creatorId',
        'name',
        'description',
        'address',
        'latitude',
        'longitude',
        'originalLink',
        'tagIds',
        'createdAt',
        'updatedAt',
        'deletedAt',
      ],
    });

    if (!togo) {
      throw new NotFoundException('ToGo place not found');
    }

    // Verify user has access to the space
    await this.verifySpaceAccess(togo.spaceId, userId);

    return togo;
  }



  async update(
    id: string,
    userId: string,
    updateToGoDto: UpdateToGoDto,
  ): Promise<ToGo> {
    const togo = await this.findOne(id, userId);

    // Only creator can update the togo
    if (togo.creatorId !== userId) {
      throw new ForbiddenException('Only the creator can update this place');
    }

    // Prepare update data
    const updateData: any = { ...updateToGoDto };

    // Handle tags update if provided - store tag IDs directly in togos table
    if (updateToGoDto.tags !== undefined) {
      try {
        console.log(`🏷️ Updating tags for ToGo: ${id}, tags: ${JSON.stringify(updateToGoDto.tags)}`);

        // Get or create tags
        const tags = updateToGoDto.tags && updateToGoDto.tags.length > 0
          ? await this.tagService.getOrCreateTags(togo.spaceId, userId, updateToGoDto.tags)
          : [];

        console.log(`🏷️ Target tags: ${JSON.stringify(tags.map(t => ({ id: t.id, name: t.name })))}`);

        // Store tag IDs as JSON array directly in the togos table
        const tagIds = tags.map(t => t.id);
        updateData.tagIds = tagIds.length > 0 ? JSON.stringify(tagIds) : null;

        console.log(`✅ Successfully updated tags for ToGo: ${id}`);
      } catch (error) {
        console.error('❌ Error updating tags:', error);
        throw error;
      }

      // Remove tags from updateData since we handled it separately
      delete updateData.tags;
    }

    await this.togoRepository.update(id, updateData);
    return this.findOne(id, userId);
  }

  async remove(id: string, userId: string): Promise<void> {
    const togo = await this.findOne(id, userId);

    // Only creator can delete the togo
    if (togo.creatorId !== userId) {
      throw new ForbiddenException('Only the creator can delete this place');
    }

    // Decrement usage count for associated tags before deletion
    if (togo.tagIds) {
      try {
        const tagIds = JSON.parse(togo.tagIds);
        for (const tagId of tagIds) {
          await this.tagService.decrementUsageCount(tagId);
        }
      } catch (error) {
        console.error('❌ Error decrementing tag usage count:', error);
        // Continue with deletion even if tag count update fails
      }
    }

    // Soft delete the togo
    await this.togoRepository.softDelete(id);

    // If there's an associated message, soft delete it too to maintain data consistency
    if (togo.messageId) {
      await this.messageRepository.softDelete(togo.messageId);
    }
  }

  // Note: Space tags are now managed through the unified Tag system
  // Use the Tag API endpoints instead

  /**
   * Load tags for togos based on their tagIds field
   */
  private async loadTagsForTogos(togos: ToGo[]): Promise<ToGo[]> {
    if (!togos.length) return togos;

    // Collect all unique tag IDs
    const allTagIds = new Set<string>();
    togos.forEach(togo => {
      if (togo.tagIds) {
        try {
          const tagIds = JSON.parse(togo.tagIds);
          tagIds.forEach((id: string) => allTagIds.add(id));
        } catch {
          // Ignore invalid JSON
        }
      }
    });

    if (allTagIds.size === 0) return togos;

    // Load all tags in one query
    const tags = await this.tagRepository.find({
      where: { id: In(Array.from(allTagIds)) }
    });
    const tagMap = new Map(tags.map(tag => [tag.id, tag]));

    // Attach tags to togos
    return togos.map(togo => {
      if (togo.tagIds) {
        try {
          const tagIds = JSON.parse(togo.tagIds);
          (togo as any).tags = tagIds
            .map((id: string) => tagMap.get(id))
            .filter(Boolean);
        } catch {
          (togo as any).tags = [];
        }
      } else {
        (togo as any).tags = [];
      }
      return togo;
    });
  }

  private async verifySpaceAccess(spaceId: string, userId: string): Promise<void> {
    // Check if space exists
    const space = await this.spaceRepository.findOne({
      where: { id: spaceId },
    });

    if (!space) {
      throw new NotFoundException('Space not found');
    }

    // Check if user is a member of the space
    const membership = await this.spaceMemberRepository.findOne({
      where: { spaceId, userId },
    });

    if (!membership) {
      throw new ForbiddenException('You are not a member of this space');
    }
  }
}
