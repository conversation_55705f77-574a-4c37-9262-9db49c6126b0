import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SettlesService } from './settles.service';
import { SettlesController } from './settles.controller';
import { Settle } from '../../entities/settle.entity';
import { Message } from '../../entities/message.entity';
import { CurrencyModule } from '../currency/currency.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Settle, Message]),
    CurrencyModule,
  ],
  controllers: [SettlesController],
  providers: [SettlesService],
  exports: [SettlesService],
})
export class SettlesModule {}
