import {
  Controller,
  Get,
  Param,
  Request,
  HttpStatus,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { SettlesService } from './settles.service';
import { ApiResponseDto } from '../../common/dto/api-response.dto';

@ApiTags('settles')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('settles')
export class SettlesController {
  constructor(private readonly settlesService: SettlesService) {}


}
