import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Settle } from '../../entities/settle.entity';
import { Message } from '../../entities/message.entity';
import { CurrencyService } from '../currency/currency.service';

@Injectable()
export class SettlesService {
  constructor(
    @InjectRepository(Settle)
    private readonly settleRepository: Repository<Settle>,
    @InjectRepository(Message)
    private readonly messageRepository: Repository<Message>,
    private readonly currencyService: CurrencyService,
  ) {}

  /**
   * Create a new settle record
   */
  async createSettle(data: {
    spaceId: string;
    messageId?: string;
    fromUserId: string;
    fromUserName: string;
    toUserId: string;
    toUserName: string;
    amount: number;
    currency: string;
    description?: string;
  }): Promise<Settle> {
    const settleData = {
      spaceId: data.spaceId,
      messageId: data.messageId,
      fromUserId: data.fromUserId,
      fromUserName: data.fromUserName,
      toUserId: data.toUserId,
      toUserName: data.toUserName,
      amount: data.amount,
      currency: data.currency,
      originalAmount: data.amount,
      originalCurrency: data.currency,
      description: data.description,
    };

    const settle = this.settleRepository.create(settleData);
    return this.settleRepository.save(settle);
  }

  /**
   * Find settle by ID
   */
  async findById(id: string): Promise<Settle | null> {
    return this.settleRepository.findOne({ where: { id } });
  }



  /**
   * Find settles by space ID
   */
  async findBySpaceId(spaceId: string): Promise<Settle[]> {
    return this.settleRepository.find({
      where: { spaceId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Delete settle by ID
   */
  async deleteSettle(id: string): Promise<void> {
    // First find the settle to get the messageId
    const settle = await this.settleRepository.findOne({
      where: { id },
      withDeleted: true, // Include soft-deleted records
    });

    if (!settle) {
      throw new NotFoundException('Settle not found');
    }

    // Check if settle is already deleted
    if (settle.deletedAt) {
      throw new NotFoundException('Settle has already been deleted');
    }

    // Soft delete the settle entity
    await this.settleRepository.softDelete(id);

    // Also soft delete the corresponding chat message if it exists
    if (settle.messageId) {
      await this.messageRepository.softDelete(settle.messageId);
    }
  }

  /**
   * Convert all settles in a space to new currency when space currency changes
   */
  async convertAllSettlesToNewCurrency(spaceId: string, newCurrency: string): Promise<void> {
    // Get all settle records for this space
    const settles = await this.settleRepository.find({
      where: { spaceId },
    });

    if (settles.length === 0) {
      return;
    }

    console.log(`Converting ${settles.length} settle records to ${newCurrency} for space ${spaceId}`);

    // Convert each settle record to new currency
    for (const settle of settles) {
      try {
        // Convert from original currency to new space currency
        if (settle.originalCurrency !== newCurrency) {
          const conversion = await this.currencyService.convertCurrency(
            Math.abs(settle.originalAmount),
            settle.originalCurrency,
            newCurrency,
          );

          // Apply the original sign to the converted amount
          const convertedAmount = settle.originalAmount >= 0
            ? conversion.convertedAmount
            : -conversion.convertedAmount;

          // Update the settle record with new conversion
          await this.settleRepository.update(settle.id, {
            amount: convertedAmount,
            currency: newCurrency,
            convertedAmount,
            convertedCurrency: newCurrency,
            exchangeRate: conversion.exchangeRate,
            convertedAt: conversion.convertedAt,
          });
        } else {
          // Same currency, clear conversion fields
          await this.settleRepository.update(settle.id, {
            amount: settle.originalAmount,
            currency: newCurrency,
            convertedAmount: null,
            convertedCurrency: null,
            exchangeRate: null,
            convertedAt: null,
          });
        }

      } catch (error) {
        console.error(`Failed to convert settle ${settle.id}:`, error);
        // Continue with other records even if one fails
      }
    }

    console.log(`Successfully converted all settle records to ${newCurrency} for space ${spaceId}`);
  }
}
