import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Balance } from './entities/balance.entity';
import { SpaceMember } from '../../entities/space-member.entity';
import { Message } from '../../entities/message.entity';
import { Space } from '../../entities/space.entity';
import { Split } from '../../entities/split.entity';
import { SplitParticipant } from '../../entities/split-participant.entity';
import { Settle } from '../../entities/settle.entity';
import { CreateBalanceDto, UpdateBalanceDto, BalanceSummaryDto, UserBalanceDto, CurrencyBalanceDto } from './dto/balance.dto';
import { CurrencyService } from '../currency/currency.service';
import { SettlesService } from '../settles/settles.service';

@Injectable()
export class BalanceService {
  constructor(
    @InjectRepository(Balance)
    private readonly balanceRepository: Repository<Balance>,
    @InjectRepository(SpaceMember)
    private readonly spaceMemberRepository: Repository<SpaceMember>,
    @InjectRepository(Message)
    private readonly messageRepository: Repository<Message>,
    @InjectRepository(Space)
    private readonly spaceRepository: Repository<Space>,
    @InjectRepository(Split)
    private readonly splitRepository: Repository<Split>,
    @InjectRepository(SplitParticipant)
    private readonly splitParticipantRepository: Repository<SplitParticipant>,
    @InjectRepository(Settle)
    private readonly settleRepository: Repository<Settle>,
    private readonly currencyService: CurrencyService,
    private readonly settlesService: SettlesService,
  ) {}

  /**
   * Get balance summary for a space
   */
  async getSpaceBalanceSummary(spaceId: string, requestingUserId: string): Promise<BalanceSummaryDto> {
    // Check if requesting user is a member of the space
    await this.checkSpaceMembership(spaceId, requestingUserId);

    // Get space information to determine default currency first
    const space = await this.spaceRepository.findOne({
      where: { id: spaceId },
    });

    if (!space) {
      throw new NotFoundException('Space not found');
    }

    const spaceCurrency = space.currency || 'USD';

    // Get all non-deleted balances for this space
    const balances = await this.balanceRepository.find({
      where: { spaceId },
      order: { updatedAt: 'DESC' },
      // Don't include deleted balances in calculations
    });

    // Calculate user balances by currency
    const userBalanceMap = new Map<string, UserBalanceDto>();

    // Initialize all space members
    const spaceMembers = await this.spaceMemberRepository.find({
      where: { spaceId },
      relations: ['user'],
    });

    for (const member of spaceMembers) {
      userBalanceMap.set(member.userId, {
        userId: member.userId,
        userName: member.user.displayName,
        currencyBalances: [],
        isSettled: true,
        owesOthers: false,
      });
    }

    // Calculate total expenses from non-deleted splits, using converted amounts
    const splits = await this.splitRepository.find({
      where: { spaceId },
      // Explicitly exclude soft-deleted splits from expense calculations
    });

    let totalExpenses = 0;

    for (const split of splits) {
      // Use converted amount if available, otherwise use original amount
      if (split.convertedAmount && split.convertedCurrency === spaceCurrency) {
        totalExpenses += split.convertedAmount;
      } else if (split.originalCurrency === spaceCurrency) {
        totalExpenses += split.totalAmount;
      } else {
        // Convert using currency service if no converted amount stored
        const conversion = await this.currencyService.convertCurrency(
          split.totalAmount,
          split.originalCurrency,
          spaceCurrency
        );
        totalExpenses += conversion.convertedAmount;
      }
    }

    // Calculate balances using space's default currency (converted amounts)
    // Create user pairs map for the space currency
    const userPairs = new Map<string, Map<string, number>>();

    // Initialize user pairs
    for (const member1 of spaceMembers) {
      userPairs.set(member1.userId, new Map<string, number>());
      for (const member2 of spaceMembers) {
        if (member1.userId !== member2.userId) {
          userPairs.get(member1.userId)!.set(member2.userId, 0);
        }
      }
    }

    // Process all balance records using converted amounts in space currency
    for (const balance of balances) {
      const fromUserId = balance.fromUserId;
      const toUserId = balance.toUserId;

      // Use amount field (which contains converted amount in space currency)
      const amount = balance.amount;

      if (userPairs.has(fromUserId) && userPairs.get(fromUserId)!.has(toUserId)) {
        const currentAmount = userPairs.get(fromUserId)!.get(toUserId)!;
        userPairs.get(fromUserId)!.set(toUserId, currentAmount + amount);
      }
    }

    // Calculate balances for each user in space currency
    for (const userBalance of userBalanceMap.values()) {
      const userId = userBalance.userId;
      let netBalance = 0;
      let totalOwed = 0;
      let totalPaid = 0;

      // Calculate net balance with all other users in space currency
      for (const otherUserId of userPairs.get(userId)!.keys()) {
        const AtoB = userPairs.get(userId)!.get(otherUserId)!; // User → Other
        const BtoA = userPairs.get(otherUserId)!.get(userId)!; // Other → User
        const netWithOther = AtoB - BtoA;

        netBalance += netWithOther;

        // Calculate totalOwed and totalPaid for display
        if (netWithOther < 0) {
          totalOwed += Math.abs(netWithOther);
        } else if (netWithOther > 0) {
          totalPaid += netWithOther;
        }
      }

      const isSettled = Math.abs(netBalance) < 0.01;
      const owesOthers = netBalance < 0; // Negative balance means user owes others

      // Create single currency balance for space currency
      const currencyBalances: CurrencyBalanceDto[] = [];

      // Only add currency balance if there's outstanding debt (not settled)
      if (!isSettled) {
        currencyBalances.push({
          currency: spaceCurrency,
          totalPaid,
          totalOwed,
          netBalance,
          isSettled,
        });
      }

      userBalance.currencyBalances = currencyBalances;
      userBalance.isSettled = isSettled;
      userBalance.owesOthers = owesOthers;
    }

    return {
      spaceId,
      spaceCurrency,
      totalExpenses,
      userBalances: Array.from(userBalanceMap.values()),
      lastUpdated: new Date(),
    };
  }

  /**
   * Get space balance overview (all user pairs debt relationships)
   */
  async getSpaceBalanceOverview(spaceId: string, requestingUserId: string): Promise<any> {
    await this.checkSpaceMembership(spaceId, requestingUserId);

    // Get space information to determine currency
    const space = await this.spaceRepository.findOne({
      where: { id: spaceId },
    });

    if (!space) {
      throw new NotFoundException('Space not found');
    }

    const spaceCurrency = space.currency || 'USD';

    // Get all balance records for this space
    const balances = await this.balanceRepository.find({
      where: { spaceId },
    });

    // Get space members
    const spaceMembers = await this.spaceMemberRepository.find({
      where: { spaceId },
      relations: ['user'],
    });

    // Calculate net balances between all user pairs using bidirectional merge
    const userPairs = new Map<string, Map<string, number>>();
    const userNames = new Map<string, string>();

    // Initialize
    for (const member of spaceMembers) {
      userPairs.set(member.userId, new Map<string, number>());
      userNames.set(member.userId, member.user.displayName);
      for (const otherMember of spaceMembers) {
        if (member.userId !== otherMember.userId) {
          userPairs.get(member.userId)!.set(otherMember.userId, 0);
        }
      }
    }

    // Process all balance records
    for (const balance of balances) {
      if (userPairs.has(balance.fromUserId) && userPairs.get(balance.fromUserId)!.has(balance.toUserId)) {
        const currentAmount = userPairs.get(balance.fromUserId)!.get(balance.toUserId)!;
        userPairs.get(balance.fromUserId)!.set(balance.toUserId, currentAmount + balance.amount);
      }
    }

    // Generate all debt relationships (including settled ones)
    const allRelationships: any[] = [];
    const processedPairs = new Set<string>();

    for (const [userId1, otherUsers] of userPairs.entries()) {
      for (const [userId2, amount] of otherUsers.entries()) {
        const pairKey1 = `${userId1}-${userId2}`;
        const pairKey2 = `${userId2}-${userId1}`;

        if (processedPairs.has(pairKey1) || processedPairs.has(pairKey2)) {
          continue;
        }

        const AtoB = userPairs.get(userId1)!.get(userId2)!;
        const BtoA = userPairs.get(userId2)!.get(userId1)!;
        const netAmount = AtoB - BtoA;

        const fromUserId = netAmount < 0 ? userId1 : userId2;
        const toUserId = netAmount < 0 ? userId2 : userId1;
        const isSettled = Math.abs(netAmount) < 0.01;

        allRelationships.push({
          fromUserId,
          fromUserName: userNames.get(fromUserId),
          toUserId,
          toUserName: userNames.get(toUserId),
          netAmount: Math.abs(netAmount),
          currency: spaceCurrency,
          isSettled,
        });

        processedPairs.add(pairKey1);
        processedPairs.add(pairKey2);
      }
    }

    return {
      currency: spaceCurrency,
      relationships: allRelationships.sort((a, b) => b.netAmount - a.netAmount),
    };
  }

  /**
   * Get all balances for a space
   */
  async getSpaceBalances(spaceId: string, requestingUserId: string): Promise<Balance[]> {
    await this.checkSpaceMembership(spaceId, requestingUserId);

    return this.balanceRepository.find({
      where: { spaceId },
      order: { updatedAt: 'DESC' },
    });
  }

  /**
   * Get recent settle records for a space (only positive payment records)
   */
  async getRecentSettles(
    spaceId: string,
    requestingUserId: string,
    limit: number = 10
  ): Promise<any[]> {
    await this.checkSpaceMembership(spaceId, requestingUserId);

    // Get settle records with their associated message information
    const settles = await this.settleRepository.find({
      where: { spaceId },
      order: { createdAt: 'DESC' },
      take: limit,
      relations: ['message'], // Include message relation to get messageId
    });

    // Transform settle records to include all necessary information
    const settleRecords = settles.map(settle => ({
      id: settle.id, // Settle ID for API operations
      messageId: settle.messageId, // Message ID for deletion (if exists)
      spaceId: settle.spaceId,
      fromUserId: settle.fromUserId,
      fromUserName: settle.fromUserName,
      toUserId: settle.toUserId,
      toUserName: settle.toUserName,
      amount: settle.amount,
      currency: settle.currency,
      originalAmount: settle.originalAmount,
      originalCurrency: settle.originalCurrency,
      description: settle.description,
      createdAt: settle.createdAt,
      updatedAt: settle.updatedAt,
    }));

    return settleRecords;
  }

  /**
   * Get recent split records for a space (from split messages)
   */
  async getRecentSplits(
    spaceId: string,
    requestingUserId: string,
    limit: number = 10
  ): Promise<any[]> {
    await this.checkSpaceMembership(spaceId, requestingUserId);

    // Get Split entities directly from the splits table
    const splits = await this.splitRepository.find({
      where: {
        spaceId,
      },
      relations: ['participants'],
      order: { createdAt: 'DESC' },
      take: limit,
      // Don't include deleted splits in recent splits
    });

    // Convert Split entities to the expected format
    return splits.map(split => {
      return {
        id: split.id, // Use the actual Split entity ID
        messageId: split.messageId, // Keep messageId for frontend compatibility
        spaceId: split.spaceId,
        title: split.title,
        description: split.description,
        category: split.category,
        totalAmount: split.totalAmount,
        currency: split.originalCurrency,
        paidByUserId: split.paidByUserId,
        paidByUserName: split.paidByUserName,
        splitType: split.splitType,
        participants: split.participants.map(p => ({
          userId: p.userId,
          userName: p.userName,
          amount: p.amount,
          isPaid: p.isPaid,
        })),
        creatorId: split.creatorId,
        createdAt: split.createdAt,
        isDeleted: !!split.deletedAt, // Add isDeleted field based on deletedAt
      };
    });

    return splits;
  }

  /**
   * Clear all balance records for a space (for testing purposes)
   */
  async clearAllBalances(
    spaceId: string,
    requestingUserId: string
  ): Promise<{ deletedCount: number; splitMessagesDeleted: number }> {
    await this.checkSpaceMembership(spaceId, requestingUserId);

    // Clear balance records
    const balanceResult = await this.balanceRepository.delete({ spaceId });

    // Also clear split messages since they create balance records
    const splitMessagesResult = await this.messageRepository.delete({
      spaceId,
      type: 'split' as any,
    });

    return {
      deletedCount: balanceResult.affected || 0,
      splitMessagesDeleted: splitMessagesResult.affected || 0,
    };
  }

  /**
   * Record a payment - creates a new payment record
   */
  async recordPayment(
    createBalanceDto: any,
    requestingUserId: string
  ): Promise<any> {
    const { spaceId, fromUserId, toUserId, amount } = createBalanceDto;

    await this.checkSpaceMembership(spaceId, requestingUserId);

    // Get space currency
    const space = await this.spaceRepository.findOne({
      where: { id: spaceId },
    });

    if (!space) {
      throw new NotFoundException('Space not found');
    }

    const spaceCurrency = space.currency || 'USD';

    // First create a settle record (without messageId for direct API calls)
    const settle = await this.settlesService.createSettle({
      spaceId,
      fromUserId,
      fromUserName: createBalanceDto.fromUserName,
      toUserId,
      toUserName: createBalanceDto.toUserName,
      amount: amount, // Positive = payment
      currency: createBalanceDto.currency || spaceCurrency,
      description: `Payment from ${createBalanceDto.fromUserName} to ${createBalanceDto.toUserName}`,
    });

    // Then create balance record linked to the settle
    const paymentRecord = await this.createBalanceRecord({
      spaceId,
      fromUserId,
      fromUserName: createBalanceDto.fromUserName,
      toUserId,
      toUserName: createBalanceDto.toUserName,
      amount: amount, // Positive = payment
      currency: createBalanceDto.currency || spaceCurrency,
      recordType: 'settle' as const,
      settleId: settle.id, // Link to the settle record
    });

    return paymentRecord;
  }



  /**
   * Create a new balance record (flow-based approach)
   * Each record represents a money flow event
   */
  async createBalanceRecord(data: {
    spaceId: string;
    fromUserId: string;
    fromUserName: string;
    toUserId: string;
    toUserName: string;
    amount: number; // Can be positive (payment) or negative (debt)
    currency: string;
    recordType: 'split' | 'settle';
    splitId?: string; // Optional link to split record
    settleId?: string; // Optional link to settle record
  }): Promise<Balance> {
    // Get space information to determine default currency
    const space = await this.spaceRepository.findOne({
      where: { id: data.spaceId },
    });

    if (!space) {
      throw new NotFoundException('Space not found');
    }

    const spaceCurrency = space.currency || 'USD';

    // Prepare balance record data
    const balanceData: any = {
      spaceId: data.spaceId,
      fromUserId: data.fromUserId,
      fromUserName: data.fromUserName,
      toUserId: data.toUserId,
      toUserName: data.toUserName,
      recordType: data.recordType,
      splitId: data.splitId, // Link to split record if provided
      settleId: data.settleId, // Link to settle record if provided
      // Store original user input
      originalAmount: data.amount,
      originalCurrency: data.currency,
    };

    // Convert currency if needed and store in amount/currency fields
    if (data.currency !== spaceCurrency) {
      try {
        const conversion = await this.currencyService.convertCurrency(
          Math.abs(data.amount), // Use absolute value for conversion
          data.currency,
          spaceCurrency,
        );

        // Apply the original sign to the converted amount
        const convertedAmount = data.amount >= 0 ? conversion.convertedAmount : -conversion.convertedAmount;

        // Store converted amount in main fields (used for calculations)
        balanceData.amount = convertedAmount;
        balanceData.currency = spaceCurrency;
        balanceData.exchangeRate = conversion.exchangeRate;
        balanceData.convertedAt = conversion.convertedAt;

        // Also store in deprecated fields for backward compatibility
        balanceData.convertedAmount = convertedAmount;
        balanceData.convertedCurrency = spaceCurrency;
      } catch (error) {
        // If conversion fails, use original values
        console.error('Currency conversion failed:', error);
        balanceData.amount = data.amount;
        balanceData.currency = data.currency;
        balanceData.exchangeRate = 1.0;
        balanceData.convertedAt = new Date();
      }
    } else {
      // Same currency, no conversion needed
      balanceData.amount = data.amount;
      balanceData.currency = spaceCurrency;
      balanceData.exchangeRate = 1.0;
      balanceData.convertedAt = new Date();

      // Also store in deprecated fields for backward compatibility
      balanceData.convertedAmount = data.amount;
      balanceData.convertedCurrency = spaceCurrency;
    }

    const balanceRecord = this.balanceRepository.create(balanceData) as unknown as Balance;

    return this.balanceRepository.save(balanceRecord);
  }

  /**
   * Calculate net balance between two users
   */
  async getNetBalance(spaceId: string, userId1: string, userId2: string): Promise<number> {
    // Get all records between these two users
    const records = await this.balanceRepository.find({
      where: [
        { spaceId, fromUserId: userId1, toUserId: userId2 },
        { spaceId, fromUserId: userId2, toUserId: userId1 },
      ],
    });

    let netBalance = 0;
    for (const record of records) {
      if (record.fromUserId === userId1) {
        // userId1 → userId2: add to balance
        netBalance += record.amount;
      } else {
        // userId2 → userId1: subtract from balance
        netBalance -= record.amount;
      }
    }

    return netBalance;
  }

  /**
   * Settle debts between two users by creating a payment record
   */
  async settleDebts(
    spaceId: string,
    fromUserId: string,
    toUserId: string,
    requestingUserId: string,
  ): Promise<{ amount: number; message: string }> {
    await this.checkSpaceMembership(spaceId, requestingUserId);

    // Verify that the requesting user is either the fromUser or toUser
    if (requestingUserId !== fromUserId && requestingUserId !== toUserId) {
      throw new ForbiddenException('You can only settle debts involving yourself');
    }

    // Calculate net debt between the two users using bidirectional merge
    const balances = await this.balanceRepository.find({
      where: { spaceId },
    });

    // Get user names
    const spaceMembers = await this.spaceMemberRepository.find({
      where: { spaceId },
      relations: ['user'],
    });

    const userNames = new Map<string, string>();
    for (const member of spaceMembers) {
      userNames.set(member.userId, member.user.displayName);
    }

    // Calculate bidirectional amounts
    let fromToAmount = 0; // fromUser → toUser
    let toFromAmount = 0; // toUser → fromUser

    for (const balance of balances) {
      if (balance.fromUserId === fromUserId && balance.toUserId === toUserId) {
        fromToAmount += balance.amount;
      } else if (balance.fromUserId === toUserId && balance.toUserId === fromUserId) {
        toFromAmount += balance.amount;
      }
    }

    // Calculate net debt: positive means fromUser owes toUser
    const netDebt = fromToAmount - toFromAmount;

    if (Math.abs(netDebt) < 0.01) {
      return {
        amount: 0,
        message: 'No debt to settle between these users',
      };
    }

    // Create payment record to settle the debt
    const paymentAmount = Math.abs(netDebt);
    const actualFromUserId = netDebt < 0 ? fromUserId : toUserId;
    const actualToUserId = netDebt < 0 ? toUserId : fromUserId;

    // Get space currency for settlement
    const space = await this.spaceRepository.findOne({
      where: { id: spaceId },
    });
    const spaceCurrency = space?.currency || 'USD';

    await this.createBalanceRecord({
      spaceId,
      fromUserId: actualFromUserId,
      fromUserName: userNames.get(actualFromUserId) || 'Unknown',
      toUserId: actualToUserId,
      toUserName: userNames.get(actualToUserId) || 'Unknown',
      amount: paymentAmount, // Positive = payment
      currency: spaceCurrency,
      recordType: 'settle' as const,
    });

    return {
      amount: paymentAmount,
      message: `${userNames.get(actualFromUserId)} paid ${userNames.get(actualToUserId)} $${paymentAmount}`,
    };
  }

  /**
   * Create a split record in the splits table
   */
  async createSplitRecord(splitData: {
    spaceId: string;
    messageId: string;
    creatorId: string;
    paidByUserId: string;
    paidByUserName: string;
    title: string;
    description?: string;
    category: string;
    splitType: string;
    totalAmount: number;
    originalCurrency: string;
    participants: Array<{
      userId: string;
      userName: string;
      amount: number;
      isPaid: boolean;
    }>;
    date: Date;
  }): Promise<any> {
    // Get space information for currency conversion
    const space = await this.spaceRepository.findOne({
      where: { id: splitData.spaceId },
    });

    if (!space) {
      throw new NotFoundException('Space not found');
    }

    const spaceCurrency = space.currency || 'USD';
    let convertedAmount = splitData.totalAmount;
    let convertedCurrency = splitData.originalCurrency;
    let exchangeRate = 1;
    let convertedAt = null;

    // Convert currency if needed
    if (splitData.originalCurrency !== spaceCurrency) {
      const conversion = await this.currencyService.convertCurrency(
        splitData.totalAmount,
        splitData.originalCurrency,
        spaceCurrency
      );
      convertedAmount = conversion.convertedAmount;
      convertedCurrency = spaceCurrency;
      exchangeRate = conversion.exchangeRate;
      convertedAt = new Date();
    }

    // Create split record
    const split = this.splitRepository.create({
      spaceId: splitData.spaceId,
      messageId: splitData.messageId,
      creatorId: splitData.creatorId,
      paidByUserId: splitData.paidByUserId,
      paidByUserName: splitData.paidByUserName,
      title: splitData.title,
      description: splitData.description,
      category: splitData.category as any, // Cast to avoid enum issues
      splitType: splitData.splitType as any, // Cast to avoid enum issues
      totalAmount: splitData.totalAmount,
      originalCurrency: splitData.originalCurrency,
      convertedAmount,
      convertedCurrency,
      exchangeRate,
      convertedAt,
      date: splitData.date,
    });

    const savedSplit = await this.splitRepository.save(split);

    // Create split participants
    for (const participant of splitData.participants) {
      let participantConvertedAmount = participant.amount;
      let participantConvertedCurrency = splitData.originalCurrency;
      let participantExchangeRate = 1;
      let participantConvertedAt = null;

      // Convert participant amount if needed
      if (splitData.originalCurrency !== spaceCurrency) {
        const conversion = await this.currencyService.convertCurrency(
          participant.amount,
          splitData.originalCurrency,
          spaceCurrency
        );
        participantConvertedAmount = conversion.convertedAmount;
        participantConvertedCurrency = spaceCurrency;
        participantExchangeRate = conversion.exchangeRate;
        participantConvertedAt = new Date();
      }

      const splitParticipant = this.splitParticipantRepository.create({
        splitId: savedSplit.id,
        userId: participant.userId,
        userName: participant.userName,
        amount: participant.amount,
        originalAmount: participant.amount,
        originalCurrency: splitData.originalCurrency,
        convertedAmount: participantConvertedAmount,
        convertedCurrency: participantConvertedCurrency,
        exchangeRate: participantExchangeRate,
        convertedAt: participantConvertedAt,
        isPaid: participant.isPaid,
        paidAt: participant.isPaid ? new Date() : null,
      });

      await this.splitParticipantRepository.save(splitParticipant);
    }

    return savedSplit;
  }

  /**
   * Update balance from split message
   */
  async updateBalanceFromSplit(
    spaceId: string,
    paidByUserId: string,
    paidByUserName: string,
    participants: Array<{
      userId: string;
      userName: string;
      amount: number;
      isPaid: boolean;
    }>,
    currency?: string,
    splitId?: string,
  ): Promise<void> {

    // Get space currency if not provided
    let splitCurrency = currency;
    if (!splitCurrency) {
      const space = await this.spaceRepository.findOne({
        where: { id: spaceId },
      });
      splitCurrency = space?.currency || 'USD';
    }

    for (const participant of participants) {
      // Skip if the participant is the one who paid
      if (participant.userId === paidByUserId) {
        continue;
      }

      // Skip if already paid
      if (participant.isPaid) {
        continue;
      }

      // Create debt record: participant owes paidByUser (negative amount)
      await this.createBalanceRecord({
        spaceId,
        fromUserId: participant.userId,
        fromUserName: participant.userName,
        toUserId: paidByUserId,
        toUserName: paidByUserName,
        amount: -participant.amount, // Negative = debt
        currency: splitCurrency,
        recordType: 'split' as const,
        splitId, // Link to the split record
      });


    }
  }

  /**
   * Soft delete balance records from split message
   */
  async deleteBalanceFromSplit(
    spaceId: string,
    paidByUserId: string,
    participants: Array<{
      userId: string;
      userName: string;
      amount: number;
      isPaid: boolean;
    }>,
    splitId?: string,
  ): Promise<void> {
    if (splitId) {
      // If we have splitId, soft delete all balance records linked to this split
      await this.balanceRepository.softDelete({
        splitId,
      });
    } else {
      // Fallback to old method for backward compatibility
      for (const participant of participants) {
        // Skip if the participant is the one who paid
        if (participant.userId === paidByUserId) {
          continue;
        }

        // Find and soft delete the debt record: participant owes paidByUser
        // We need to find records with negative amount (debt)
        await this.balanceRepository.softDelete({
          spaceId,
          fromUserId: participant.userId,
          toUserId: paidByUserId,
          amount: -participant.amount, // The negative amount we created
        });
      }
    }
  }

  /**
   * Update a settle record
   */
  async updateSettle(
    spaceId: string,
    settleId: string,
    updateData: { amount?: number; currency?: string },
    requestingUserId: string,
  ): Promise<any> {
    await this.checkSpaceMembership(spaceId, requestingUserId);

    // Find the settle record
    const settleRecord = await this.balanceRepository.findOne({
      where: { id: settleId, spaceId },
    });

    if (!settleRecord) {
      throw new NotFoundException('Settle record not found');
    }

    // Verify that the requesting user is involved in this settle
    if (requestingUserId !== settleRecord.fromUserId && requestingUserId !== settleRecord.toUserId) {
      throw new ForbiddenException('You can only update settles involving yourself');
    }

    // Update the record
    const updateFields: any = {};
    if (updateData.amount !== undefined) {
      updateFields.amount = updateData.amount;
    }
    if (updateData.currency !== undefined) {
      updateFields.currency = updateData.currency;
    }
    updateFields.updatedAt = new Date();

    await this.balanceRepository.update(settleId, updateFields);

    return await this.balanceRepository.findOne({
      where: { id: settleId },
    });
  }



  private async checkSpaceMembership(spaceId: string, userId: string): Promise<void> {
    const membership = await this.spaceMemberRepository.findOne({
      where: { spaceId, userId },
    });

    if (!membership) {
      throw new ForbiddenException('You are not a member of this space');
    }
  }

  /**
   * Batch convert all balance records when space currency changes
   */
  async convertAllBalancesToNewCurrency(spaceId: string, newCurrency: string): Promise<void> {
    // Get all balance records for this space
    const balances = await this.balanceRepository.find({
      where: { spaceId },
    });

    if (balances.length === 0) {
      return;
    }

    console.log(`Converting ${balances.length} balance records to ${newCurrency} for space ${spaceId}`);

    // Convert each balance record to new currency
    for (const balance of balances) {
      try {
        // Convert from original currency to new space currency
        const conversion = await this.currencyService.convertCurrency(
          Math.abs(balance.originalAmount),
          balance.originalCurrency,
          newCurrency,
        );

        // Apply the original sign to the converted amount
        const convertedAmount = balance.originalAmount >= 0
          ? conversion.convertedAmount
          : -conversion.convertedAmount;

        // Update the balance record with new conversion
        // Store in main fields (used for calculations) and deprecated fields (backward compatibility)
        await this.balanceRepository.update(balance.id, {
          amount: convertedAmount,
          currency: newCurrency,
          convertedAmount,
          convertedCurrency: newCurrency,
          exchangeRate: conversion.exchangeRate,
          convertedAt: conversion.convertedAt,
        });

      } catch (error) {
        console.error(`Failed to convert balance ${balance.id}:`, error);
        // Continue with other records even if one fails
      }
    }

    console.log(`Successfully converted all balance records to ${newCurrency} for space ${spaceId}`);
  }

  /**
   * Delete a settle record and its associated balance records
   */
  async deleteSettle(spaceId: string, settleId: string, currentUserId: string): Promise<any> {
    // Verify space membership
    const spaceMember = await this.spaceMemberRepository.findOne({
      where: { spaceId, userId: currentUserId },
    });

    if (!spaceMember) {
      throw new Error('User is not a member of this space');
    }

    // Find the settle record (including soft deleted ones)
    const settle = await this.settleRepository.findOne({
      where: { id: settleId, spaceId },
      withDeleted: true, // Include soft deleted records
    });

    if (!settle) {
      // If settle record doesn't exist at all, consider it already deleted
      console.log(`Settle record ${settleId} not found, considering it already deleted`);
      return {
        settleId,
        deletedAt: new Date(),
        message: 'Settle record was already deleted or does not exist',
      };
    }

    // If already soft deleted, return success
    if (settle.deletedAt) {
      console.log(`Settle record ${settleId} already soft deleted`);
      return {
        settleId,
        deletedAt: settle.deletedAt,
        message: 'Settle record was already deleted',
      };
    }

    // Soft delete the settle record
    await this.settleRepository.update(settleId, {
      deletedAt: new Date(),
    });

    // Soft delete associated balance records
    await this.balanceRepository.update(
      { settleId, spaceId },
      { deletedAt: new Date() }
    );

    // Also soft delete the associated message if it exists
    if (settle.messageId) {
      try {
        await this.messageRepository.update(settle.messageId, {
          deletedAt: new Date(),
        });
        console.log(`Soft deleted message ${settle.messageId} for settle ${settleId}`);
      } catch (error) {
        console.warn(`Failed to delete message ${settle.messageId} for settle ${settleId}:`, error);
      }
    }

    console.log(`Deleted settle ${settleId} and associated balance records`);

    return {
      settleId,
      deletedAt: new Date(),
    };
  }

  /**
   * Create settle record from message (used when creating settle messages in chat)
   */
  async createSettleFromMessage(data: {
    spaceId: string;
    messageId: string;
    fromUserId: string;
    fromUserName: string;
    toUserId: string;
    toUserName: string;
    amount: number;
    currency: string;
    description?: string;
  }): Promise<any> {
    console.log('🔄 BalanceService.createSettleFromMessage called with:', data);
    // Get space information for currency conversion
    const space = await this.spaceRepository.findOne({
      where: { id: data.spaceId },
    });

    if (!space) {
      throw new NotFoundException('Space not found');
    }

    const spaceCurrency = space.currency || 'USD';

    // Create settle record with messageId
    const settle = await this.settlesService.createSettle({
      spaceId: data.spaceId,
      messageId: data.messageId,
      fromUserId: data.fromUserId,
      fromUserName: data.fromUserName,
      toUserId: data.toUserId,
      toUserName: data.toUserName,
      amount: data.amount,
      currency: data.currency,
      description: data.description || `Payment from ${data.fromUserName} to ${data.toUserName}`,
    });

    // Create balance record linked to the settle
    const balanceRecord = await this.createBalanceRecord({
      spaceId: data.spaceId,
      fromUserId: data.fromUserId,
      fromUserName: data.fromUserName,
      toUserId: data.toUserId,
      toUserName: data.toUserName,
      amount: data.amount, // Positive = payment
      currency: data.currency,
      recordType: 'settle' as const,
      settleId: settle.id, // Link to the settle record
    });

    return { settle, balance: balanceRecord };
  }

  /**
   * Delete balance records associated with a settle record
   */
  async deleteBalanceFromSettle(settleId: string): Promise<void> {
    // Soft delete all balance records linked to this settle
    await this.balanceRepository.softDelete({
      settleId,
    });

    console.log(`Soft deleted balance records for settle: ${settleId}`);
  }

}
