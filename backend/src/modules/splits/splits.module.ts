import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SplitsController } from './splits.controller';
import { SplitsService } from './splits.service';
import { Split, SplitParticipant } from '../../entities';
import { SpaceMember } from '../../entities/space-member.entity';
import { Space } from '../../entities/space.entity';
import { Message } from '../../entities/message.entity';
import { CurrencyModule } from '../currency/currency.module';
import { BalanceModule } from '../balance/balance.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Split, SplitParticipant, SpaceMember, Space, Message]),
    CurrencyModule,
    BalanceModule,
  ],
  controllers: [SplitsController],
  providers: [SplitsService],
  exports: [SplitsService],
})
export class SplitsModule {}
