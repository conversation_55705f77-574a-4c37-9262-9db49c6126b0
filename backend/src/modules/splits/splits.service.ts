import { Injectable, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindManyOptions, Between } from 'typeorm';
import { Split, SplitParticipant } from '../../entities';
import { SpaceMember } from '../../entities/space-member.entity';
import { Space } from '../../entities/space.entity';
import { Message } from '../../entities/message.entity';
import { CurrencyService } from '../currency/currency.service';
import { BalanceService } from '../balance/balance.service';
import { CreateSplitDto, UpdateSplitDto, SplitResponseDto, SplitListQueryDto } from './dto/split.dto';

@Injectable()
export class SplitsService {
  constructor(
    @InjectRepository(Split)
    private readonly splitRepository: Repository<Split>,
    @InjectRepository(SplitParticipant)
    private readonly splitParticipantRepository: Repository<SplitParticipant>,
    @InjectRepository(SpaceMember)
    private readonly spaceMemberRepository: Repository<SpaceMember>,
    @InjectRepository(Space)
    private readonly spaceRepository: Repository<Space>,
    @InjectRepository(Message)
    private readonly messageRepository: Repository<Message>,
    private readonly currencyService: CurrencyService,
    private readonly balanceService: BalanceService,
  ) {}

  /**
   * Create a new split
   */
  async createSplit(createSplitDto: CreateSplitDto, creatorId: string): Promise<SplitResponseDto> {
    // Check if creator is a member of the space
    await this.checkSpaceMembership(createSplitDto.spaceId, creatorId);

    // Get space information for currency conversion
    const space = await this.spaceRepository.findOne({
      where: { id: createSplitDto.spaceId },
    });

    if (!space) {
      throw new NotFoundException('Space not found');
    }

    const spaceCurrency = space.currency || 'USD';

    // Validate participants are space members
    for (const participant of createSplitDto.participants) {
      await this.checkSpaceMembership(createSplitDto.spaceId, participant.userId);
    }

    // Validate total amount matches sum of participant amounts
    const totalParticipantAmount = createSplitDto.participants.reduce(
      (sum, participant) => sum + participant.amount,
      0,
    );

    if (Math.abs(totalParticipantAmount - createSplitDto.totalAmount) > 0.01) {
      throw new BadRequestException('Total amount does not match sum of participant amounts');
    }

    // Convert currency if needed
    let convertedAmount: number | undefined;
    let exchangeRate: number | undefined;
    let convertedAt: Date | undefined;

    if (createSplitDto.currency !== spaceCurrency) {
      const conversion = await this.currencyService.convertCurrency(
        createSplitDto.totalAmount,
        createSplitDto.currency,
        spaceCurrency,
      );
      convertedAmount = conversion.convertedAmount;
      exchangeRate = conversion.exchangeRate;
      convertedAt = conversion.convertedAt;
    }

    // Create split entity
    const split = this.splitRepository.create({
      spaceId: createSplitDto.spaceId,
      messageId: createSplitDto.messageId,
      creatorId,
      paidByUserId: createSplitDto.paidByUserId,
      paidByUserName: createSplitDto.paidByUserName,
      title: createSplitDto.title,
      description: createSplitDto.description,
      category: createSplitDto.category,
      splitType: createSplitDto.splitType,
      totalAmount: createSplitDto.totalAmount,
      originalCurrency: createSplitDto.currency,
      convertedAmount,
      convertedCurrency: convertedAmount ? spaceCurrency : undefined,
      exchangeRate,
      convertedAt,
      date: createSplitDto.date ? new Date(createSplitDto.date) : new Date(),
    });

    const savedSplit = await this.splitRepository.save(split);

    // Create participant entities
    const participants = [];
    for (const participantDto of createSplitDto.participants) {
      // Convert participant amount if needed
      let participantConvertedAmount: number | undefined;
      let participantExchangeRate: number | undefined;
      let participantConvertedAt: Date | undefined;

      if (createSplitDto.currency !== spaceCurrency) {
        const conversion = await this.currencyService.convertCurrency(
          participantDto.amount,
          createSplitDto.currency,
          spaceCurrency,
        );
        participantConvertedAmount = conversion.convertedAmount;
        participantExchangeRate = conversion.exchangeRate;
        participantConvertedAt = conversion.convertedAt;
      }

      const participant = this.splitParticipantRepository.create({
        splitId: savedSplit.id,
        userId: participantDto.userId,
        userName: participantDto.userName,
        amount: participantDto.amount,
        originalAmount: participantDto.amount,
        originalCurrency: createSplitDto.currency,
        convertedAmount: participantConvertedAmount,
        convertedCurrency: participantConvertedAmount ? spaceCurrency : undefined,
        exchangeRate: participantExchangeRate,
        convertedAt: participantConvertedAt,
        isPaid: participantDto.isPaid || false,
        paidAt: participantDto.paidAt ? new Date(participantDto.paidAt) : undefined,
      });

      participants.push(participant);
    }

    await this.splitParticipantRepository.save(participants);

    // Create balance records for the split
    await this.balanceService.updateBalanceFromSplit(
      createSplitDto.spaceId,
      createSplitDto.paidByUserId,
      createSplitDto.paidByUserName,
      createSplitDto.participants.map(p => ({
        userId: p.userId,
        userName: p.userName,
        amount: p.amount,
        isPaid: p.isPaid || false,
      })),
      createSplitDto.currency,
      savedSplit.id, // Link balance records to this split
    );

    // Return the created split with participants
    return this.getSplitById(savedSplit.id, creatorId);
  }

  /**
   * Get split by ID
   */
  async getSplitById(splitId: string, requestingUserId: string): Promise<SplitResponseDto> {
    const split = await this.splitRepository.findOne({
      where: { id: splitId },
      relations: ['participants'],
    });

    if (!split) {
      throw new NotFoundException('Split not found');
    }

    // Check if requesting user is a member of the space
    await this.checkSpaceMembership(split.spaceId, requestingUserId);

    return this.mapSplitToResponseDto(split);
  }



  /**
   * Get splits for a space
   */
  async getSpaceSplits(
    spaceId: string,
    requestingUserId: string,
    query: SplitListQueryDto,
  ): Promise<{ splits: SplitResponseDto[]; total: number; page: number; limit: number }> {
    // Check if requesting user is a member of the space
    await this.checkSpaceMembership(spaceId, requestingUserId);

    const { page = 1, limit = 20, category, userId, startDate, endDate } = query;
    const skip = (page - 1) * limit;

    // Build query conditions
    const where: any = { spaceId };

    if (category) {
      where.category = category;
    }



    if (startDate || endDate) {
      where.createdAt = Between(
        startDate ? new Date(startDate) : new Date('1900-01-01'),
        endDate ? new Date(endDate) : new Date(),
      );
    }

    const findOptions: FindManyOptions<Split> = {
      where,
      relations: ['participants'],
      order: { createdAt: 'DESC' },
      skip,
      take: limit,
    };

    // If filtering by user, we need to check both creator and participants
    if (userId) {
      const [creatorSplits, creatorTotal] = await this.splitRepository.findAndCount({
        ...findOptions,
        where: { ...where, creatorId: userId },
      });

      const [participantSplits, participantTotal] = await this.splitRepository
        .createQueryBuilder('split')
        .leftJoinAndSelect('split.participants', 'participant')
        .where('split.spaceId = :spaceId', { spaceId })
        .andWhere('participant.userId = :userId', { userId })
        .orderBy('split.createdAt', 'DESC')
        .skip(skip)
        .take(limit)
        .getManyAndCount();

      // Combine and deduplicate results
      const allSplits = [...creatorSplits, ...participantSplits];
      const uniqueSplits = allSplits.filter(
        (split, index, self) => index === self.findIndex((s) => s.id === split.id),
      );

      return {
        splits: uniqueSplits.map((split) => this.mapSplitToResponseDto(split)),
        total: creatorTotal + participantTotal,
        page,
        limit,
      };
    }

    const [splits, total] = await this.splitRepository.findAndCount(findOptions);

    return {
      splits: splits.map((split) => this.mapSplitToResponseDto(split)),
      total,
      page,
      limit,
    };
  }

  /**
   * Update split
   */
  async updateSplit(
    splitId: string,
    updateSplitDto: UpdateSplitDto,
    requestingUserId: string,
  ): Promise<SplitResponseDto> {
    const split = await this.splitRepository.findOne({
      where: { id: splitId },
      relations: ['participants'],
    });

    if (!split) {
      throw new NotFoundException('Split not found');
    }

    // Check if requesting user is the creator or a space admin
    if (split.creatorId !== requestingUserId) {
      await this.checkSpaceMembership(split.spaceId, requestingUserId);
      // Additional admin check could be added here
    }

    // Update split fields
    Object.assign(split, updateSplitDto);

    // Handle currency conversion if currency changed
    if (updateSplitDto.currency && updateSplitDto.currency !== split.originalCurrency) {
      const space = await this.spaceRepository.findOne({
        where: { id: split.spaceId },
      });
      const spaceCurrency = space?.currency || 'USD';

      if (updateSplitDto.currency !== spaceCurrency) {
        const conversion = await this.currencyService.convertCurrency(
          updateSplitDto.totalAmount || split.totalAmount,
          updateSplitDto.currency,
          spaceCurrency,
        );
        split.convertedAmount = conversion.convertedAmount;
        split.convertedCurrency = spaceCurrency;
        split.exchangeRate = conversion.exchangeRate;
        split.convertedAt = conversion.convertedAt;
      } else {
        split.convertedAmount = undefined;
        split.convertedCurrency = undefined;
        split.exchangeRate = undefined;
        split.convertedAt = undefined;
      }
    }

    const updatedSplit = await this.splitRepository.save(split);
    return this.mapSplitToResponseDto(updatedSplit);
  }

  /**
   * Delete split
   */
  async deleteSplit(splitId: string, requestingUserId: string): Promise<void> {
    const split = await this.splitRepository.findOne({
      where: { id: splitId },
      withDeleted: true, // Include soft-deleted records
    });

    if (!split) {
      throw new NotFoundException('Split not found');
    }

    // Check if split is already deleted
    if (split.deletedAt) {
      throw new NotFoundException('Split has already been deleted');
    }

    // Check if requesting user is the creator or a space admin
    if (split.creatorId !== requestingUserId) {
      await this.checkSpaceMembership(split.spaceId, requestingUserId);
      // Additional admin check could be added here
    }

    // Delete related balance records first
    await this.balanceService.deleteBalanceFromSplit(
      split.spaceId,
      split.paidByUserId,
      split.participants.map(p => ({
        userId: p.userId,
        userName: p.userName,
        amount: p.amount,
        isPaid: p.isPaid,
      })),
      splitId, // Use splitId for efficient deletion
    );

    // Soft delete the split entity
    await this.splitRepository.softDelete(splitId);

    // Also soft delete the corresponding chat message if it exists
    if (split.messageId) {
      await this.messageRepository.softDelete(split.messageId);
    }
  }

  /**
   * Check if user is a member of the space
   */
  private async checkSpaceMembership(spaceId: string, userId: string): Promise<void> {
    const membership = await this.spaceMemberRepository.findOne({
      where: { spaceId, userId },
    });

    if (!membership) {
      throw new ForbiddenException('User is not a member of this space');
    }
  }

  /**
   * Convert all splits in a space to new currency when space currency changes
   */
  async convertAllSplitsToNewCurrency(spaceId: string, newCurrency: string): Promise<void> {
    // Get all split records for this space
    const splits = await this.splitRepository.find({
      where: { spaceId },
      relations: ['participants'],
    });

    if (splits.length === 0) {
      return;
    }

    console.log(`Converting ${splits.length} split records to ${newCurrency} for space ${spaceId}`);

    // Convert each split record to new currency
    for (const split of splits) {
      try {
        // Convert split total amount
        if (split.originalCurrency !== newCurrency) {
          const conversion = await this.currencyService.convertCurrency(
            split.totalAmount,
            split.originalCurrency,
            newCurrency,
          );

          // Update split with new conversion
          await this.splitRepository.update(split.id, {
            convertedAmount: conversion.convertedAmount,
            convertedCurrency: newCurrency,
            exchangeRate: conversion.exchangeRate,
            convertedAt: conversion.convertedAt,
          });

          // Convert all participants
          for (const participant of split.participants) {
            if (participant.originalCurrency !== newCurrency) {
              const participantConversion = await this.currencyService.convertCurrency(
                participant.originalAmount,
                participant.originalCurrency,
                newCurrency,
              );

              await this.splitParticipantRepository.update(participant.id, {
                convertedAmount: participantConversion.convertedAmount,
                convertedCurrency: newCurrency,
                exchangeRate: participantConversion.exchangeRate,
                convertedAt: participantConversion.convertedAt,
              });
            } else {
              // Same currency, clear conversion fields
              await this.splitParticipantRepository.update(participant.id, {
                convertedAmount: null,
                convertedCurrency: null,
                exchangeRate: null,
                convertedAt: null,
              });
            }
          }
        } else {
          // Same currency, clear conversion fields for split
          await this.splitRepository.update(split.id, {
            convertedAmount: null,
            convertedCurrency: null,
            exchangeRate: null,
            convertedAt: null,
          });

          // Clear conversion fields for participants
          for (const participant of split.participants) {
            await this.splitParticipantRepository.update(participant.id, {
              convertedAmount: null,
              convertedCurrency: null,
              exchangeRate: null,
              convertedAt: null,
            });
          }
        }

      } catch (error) {
        console.error(`Failed to convert split ${split.id}:`, error);
        // Continue with other records even if one fails
      }
    }

    console.log(`Successfully converted all split records to ${newCurrency} for space ${spaceId}`);
  }

  /**
   * Map Split entity to response DTO
   */
  private mapSplitToResponseDto(split: Split): SplitResponseDto {
    return {
      id: split.id,
      spaceId: split.spaceId,
      messageId: split.messageId,
      creatorId: split.creatorId,
      paidByUserId: split.paidByUserId,
      paidByUserName: split.paidByUserName,
      title: split.title,
      description: split.description,
      category: split.category,
      splitType: split.splitType,
      totalAmount: split.totalAmount,
      originalCurrency: split.originalCurrency,
      convertedAmount: split.convertedAmount,
      convertedCurrency: split.convertedCurrency,
      exchangeRate: split.exchangeRate,
      convertedAt: split.convertedAt,
      date: split.date,
      participants: split.participants.map((participant) => ({
        id: participant.id,
        userId: participant.userId,
        userName: participant.userName,
        amount: participant.amount,
        originalAmount: participant.originalAmount,
        originalCurrency: participant.originalCurrency,
        convertedAmount: participant.convertedAmount,
        convertedCurrency: participant.convertedCurrency,
        exchangeRate: participant.exchangeRate,
        convertedAt: participant.convertedAt,
        isPaid: participant.isPaid,
        paidAt: participant.paidAt,
        createdAt: participant.createdAt,
        updatedAt: participant.updatedAt,
      })),
      createdAt: split.createdAt,
      updatedAt: split.updatedAt,
    };
  }
}
