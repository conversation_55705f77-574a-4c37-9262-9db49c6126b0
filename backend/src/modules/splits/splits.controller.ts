import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  Request,
  UseGuards,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { SplitsService } from './splits.service';
import {
  CreateSplitDto,
  UpdateSplitDto,
  SplitResponseDto,
  SplitListQueryDto,
} from './dto/split.dto';
import { ApiResponseDto } from '../../common/dto/api-response.dto';

@ApiTags('Splits')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('splits')
export class SplitsController {
  constructor(private readonly splitsService: SplitsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new split' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Split created successfully',
    type: SplitResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'User is not a member of the space',
  })
  async createSplit(
    @Body() createSplitDto: CreateSplitDto,
    @Request() req,
  ): Promise<ApiResponseDto<SplitResponseDto>> {
    const split = await this.splitsService.createSplit(createSplitDto, req.user.id);

    return {
      success: true,
      data: split,
      message: 'Split created successfully',
      timestamp: new Date().toISOString(),
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get split by ID' })
  @ApiParam({ name: 'id', description: 'Split ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Split retrieved successfully',
    type: SplitResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Split not found',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'User is not a member of the space',
  })
  async getSplitById(
    @Param('id') splitId: string,
    @Request() req,
  ): Promise<ApiResponseDto<SplitResponseDto>> {
    const split = await this.splitsService.getSplitById(splitId, req.user.id);

    return {
      success: true,
      data: split,
      message: 'Split retrieved successfully',
      timestamp: new Date().toISOString(),
    };
  }



  @Get('spaces/:spaceId')
  @ApiOperation({ summary: 'Get splits for a space' })
  @ApiParam({ name: 'spaceId', description: 'Space ID' })
  @ApiQuery({ name: 'page', required: false, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page' })
  @ApiQuery({ name: 'category', required: false, description: 'Filter by category' })
  @ApiQuery({ name: 'isSettled', required: false, description: 'Filter by settlement status' })
  @ApiQuery({ name: 'userId', required: false, description: 'Filter by user ID' })
  @ApiQuery({ name: 'startDate', required: false, description: 'Start date for filtering' })
  @ApiQuery({ name: 'endDate', required: false, description: 'End date for filtering' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Splits retrieved successfully',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'User is not a member of the space',
  })
  async getSpaceSplits(
    @Param('spaceId') spaceId: string,
    @Query() query: SplitListQueryDto,
    @Request() req,
  ): Promise<ApiResponseDto<{
    splits: SplitResponseDto[];
    total: number;
    page: number;
    limit: number;
  }>> {
    const result = await this.splitsService.getSpaceSplits(spaceId, req.user.id, query);

    return {
      success: true,
      data: result,
      message: 'Splits retrieved successfully',
      timestamp: new Date().toISOString(),
    };
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update split' })
  @ApiParam({ name: 'id', description: 'Split ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Split updated successfully',
    type: SplitResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Split not found',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'User is not authorized to update this split',
  })
  async updateSplit(
    @Param('id') splitId: string,
    @Body() updateSplitDto: UpdateSplitDto,
    @Request() req,
  ): Promise<ApiResponseDto<SplitResponseDto>> {
    const split = await this.splitsService.updateSplit(splitId, updateSplitDto, req.user.id);

    return {
      success: true,
      data: split,
      message: 'Split updated successfully',
      timestamp: new Date().toISOString(),
    };
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete split' })
  @ApiParam({ name: 'id', description: 'Split ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Split deleted successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Split not found',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'User is not authorized to delete this split',
  })
  async deleteSplit(
    @Param('id') splitId: string,
    @Request() req,
  ): Promise<ApiResponseDto<null>> {
    await this.splitsService.deleteSplit(splitId, req.user.id);

    return {
      success: true,
      data: null,
      message: 'Split deleted successfully',
      timestamp: new Date().toISOString(),
    };
  }
}
