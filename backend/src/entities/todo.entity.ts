import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from './user.entity';
import { Space } from './space.entity';
import { Message } from './message.entity';


export enum TodoStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

export enum TodoParticipantRole {
  ASSIGNEE = 'assignee',
  COLLABORATOR = 'collaborator',
  OBSERVER = 'observer',
}

@Entity('todos')
@Index(['spaceId'])
@Index(['creatorId'])
@Index(['status'])
@Index(['dueDate'])
@Index(['createdAt'])
@Index(['spaceId', 'createdAt'])
@Index(['spaceId', 'status'])
export class Todo {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'space_id' })
  spaceId: string;

  @Column({ name: 'message_id', nullable: true })
  messageId?: string;

  @Column({ name: 'creator_id' })
  creatorId: string;

  @Column({ length: 200 })
  title: string;

  @Column({ type: 'text', nullable: true })
  details?: string;

  @Column({
    type: 'varchar',
    length: 20,
    enum: TodoStatus,
    default: TodoStatus.PENDING,
  })
  status: TodoStatus;

  @Column({ name: 'due_date', type: 'date', nullable: true })
  dueDate?: Date;

  @Column({ name: 'due_time', type: 'time', nullable: true })
  dueTime?: string;

  @Column({ type: 'text', nullable: true })
  location?: string;

  @Column({ name: 'tag_ids', type: 'text', nullable: true })
  tagIds?: string;

  @Column({ name: 'participant_data', type: 'text', nullable: true })
  participantData?: string;

  @Column({ name: 'completed_at', nullable: true })
  completedAt?: Date;

  @Column({ name: 'completed_by_user_id', nullable: true })
  completedByUserId?: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @DeleteDateColumn({ name: 'deleted_at' })
  deletedAt?: Date;

  // Relations
  @ManyToOne(() => Space, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'space_id' })
  space: Space;

  @OneToOne(() => Message, (message) => message.todo, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'message_id' })
  message?: Message;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'creator_id' })
  creator: User;

  @ManyToOne(() => User, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'completed_by_user_id' })
  completedByUser?: User;

  // Virtual property to get participants as array
  get participantsArray(): any[] {
    if (!this.participantData) return [];
    try {
      return JSON.parse(this.participantData);
    } catch {
      return [];
    }
  }

  // Virtual property to set participants from array
  set participantsArray(participants: any[]) {
    this.participantData = participants.length > 0 ? JSON.stringify(participants) : null;
  }

  // Virtual property to get tag IDs as array
  get tagIdsArray(): string[] {
    if (!this.tagIds) return [];
    try {
      return JSON.parse(this.tagIds);
    } catch {
      return [];
    }
  }

  // Virtual property to set tag IDs from array
  set tagIdsArray(tagIds: string[]) {
    this.tagIds = tagIds.length > 0 ? JSON.stringify(tagIds) : null;
  }

  constructor(partial: Partial<Todo>) {
    Object.assign(this, partial);
  }
}
