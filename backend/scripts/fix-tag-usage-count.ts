import { DataSource } from 'typeorm';
import { Tag } from '../src/entities/tag.entity';
import { Todo } from '../src/entities/todo.entity';
import { ToGo } from '../src/entities/togo.entity';

// Database configuration
const dataSource = new DataSource({
  type: 'sqlite',
  database: './data/melo_dev.db',
  entities: [Tag, Todo, ToGo],
  synchronize: false,
});

async function fixTagUsageCount() {
  try {
    await dataSource.initialize();
    console.log('🔗 Database connected');

    const tagRepository = dataSource.getRepository(Tag);
    const todoRepository = dataSource.getRepository(Todo);
    const togoRepository = dataSource.getRepository(ToGo);

    // Get all tags
    const tags = await tagRepository.find();
    console.log(`📊 Found ${tags.length} tags to check`);

    for (const tag of tags) {
      console.log(`\n🏷️ Checking tag: ${tag.name} (current count: ${tag.usageCount})`);

      // Count actual usage in active (non-deleted) todos
      const activeTodosWithTag = await todoRepository
        .createQueryBuilder('todo')
        .where('todo.tag_ids LIKE :tagId', { tagId: `%"${tag.id}"%` })
        .andWhere('todo.deleted_at IS NULL')
        .getCount();

      // Count actual usage in active (non-deleted) togos
      const activeTogosWithTag = await togoRepository
        .createQueryBuilder('togo')
        .where('togo.tag_ids LIKE :tagId', { tagId: `%"${tag.id}"%` })
        .andWhere('togo.deleted_at IS NULL')
        .getCount();

      const actualUsageCount = activeTodosWithTag + activeTogosWithTag;
      
      console.log(`  📝 Active TODOs using this tag: ${activeTodosWithTag}`);
      console.log(`  📍 Active ToGos using this tag: ${activeTogosWithTag}`);
      console.log(`  🔢 Actual usage count: ${actualUsageCount}`);
      console.log(`  💾 Stored usage count: ${tag.usageCount}`);

      if (tag.usageCount !== actualUsageCount) {
        console.log(`  ⚠️ Mismatch detected! Updating from ${tag.usageCount} to ${actualUsageCount}`);
        
        await tagRepository.update(tag.id, { usageCount: actualUsageCount });
        console.log(`  ✅ Updated tag "${tag.name}" usage count to ${actualUsageCount}`);
      } else {
        console.log(`  ✅ Tag "${tag.name}" usage count is correct`);
      }
    }

    console.log('\n🎉 Tag usage count fix completed!');

  } catch (error) {
    console.error('❌ Error fixing tag usage count:', error);
  } finally {
    await dataSource.destroy();
    console.log('🔌 Database connection closed');
  }
}

// Run the fix
fixTagUsageCount();
