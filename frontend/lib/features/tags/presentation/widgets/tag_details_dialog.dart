import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/models/tag_model.dart';
import '../../../../core/services/tag_api_service.dart';
import '../../../../core/di/dependency_injection.dart';

class TagDetailsDialog extends StatefulWidget {
  final String spaceId;
  final TagData tag;
  final VoidCallback? onTagDeleted;

  const TagDetailsDialog({
    super.key,
    required this.spaceId,
    required this.tag,
    this.onTagDeleted,
  });

  @override
  State<TagDetailsDialog> createState() => _TagDetailsDialogState();
}

class _TagDetailsDialogState extends State<TagDetailsDialog> {
  final TagApiService _tagApiService =
      DependencyInjection.getIt<TagApiService>();
  TagRelations? _tagRelations;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadTagRelations();
  }

  Future<void> _loadTagRelations() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final response = await _tagApiService.getTagRelations(
        widget.spaceId,
        widget.tag.id,
      );

      setState(() {
        _tagRelations = response.data;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Tag'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
                'Are you sure you want to delete the tag "${widget.tag.name}"?'),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.warning.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppColors.warning),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.warning_amber,
                    color: AppColors.warning,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'This will remove the tag from all ${_tagRelations?.totalCount ?? 0} related items, but the items themselves will not be deleted.',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context, rootNavigator: true).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context, rootNavigator: true).pop();
              await _deleteTag();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteTag() async {
    try {
      await _tagApiService.deleteTag(widget.spaceId, widget.tag.id);

      widget.onTagDeleted?.call();
      Navigator.of(context).pop();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Tag "${widget.tag.name}" deleted successfully'),
          backgroundColor: AppColors.success,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to delete tag: ${e.toString()}'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  void _navigateToItem(TagRelatedItem item) {
    Navigator.of(context, rootNavigator: true).pop(); // Close dialog first

    if (item.type == 'todo') {
      // Navigate to TODO detail or TODO tab
      context.go('/spaces/${widget.spaceId}?tab=4'); // TODOs tab index
    } else if (item.type == 'togo') {
      // Navigate to ToGo detail or ToGo tab
      context.go('/spaces/${widget.spaceId}?tab=6'); // ToGo tab index
    }
  }

  Color get _tagColor {
    try {
      return Color(int.parse(widget.tag.color.replaceFirst('#', '0xFF')));
    } catch (e) {
      return AppColors.primary;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: _tagColor,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.tag.name,
                        style:
                            Theme.of(context).textTheme.headlineSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                      ),
                      if (widget.tag.description != null &&
                          widget.tag.description!.isNotEmpty)
                        Text(
                          widget.tag.description!,
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: AppColors.textSecondary,
                                  ),
                        ),
                    ],
                  ),
                ),
                IconButton(
                  icon: Icon(
                    Icons.delete,
                    color: AppColors.error,
                  ),
                  onPressed: _showDeleteConfirmation,
                  tooltip: 'Delete tag',
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () =>
                      Navigator.of(context, rootNavigator: true).pop(),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Stats
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.surface,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppColors.border),
              ),
              child: Row(
                children: [
                  _buildStatItem(
                    'Usage Count',
                    '${widget.tag.usageCount}',
                    Icons.trending_up,
                  ),
                  const SizedBox(width: 24),
                  _buildStatItem(
                    'Related Items',
                    '${_tagRelations?.totalCount ?? 0}',
                    Icons.link,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // Related Items
            Text(
              'Related Items',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 8),

            Expanded(
              child: _buildRelatedItemsList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          size: 20,
          color: AppColors.textSecondary,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.textSecondary,
              ),
        ),
      ],
    );
  }

  Widget _buildRelatedItemsList() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: AppColors.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Error loading related items',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textSecondary,
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadTagRelations,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    final relatedItems = _tagRelations?.relatedItems ?? [];

    if (relatedItems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.link_off,
              size: 48,
              color: AppColors.textSecondary,
            ),
            const SizedBox(height: 16),
            Text(
              'No related items',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: AppColors.textSecondary,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'This tag is not currently used in any TODOs or ToGos',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textSecondary,
                  ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: relatedItems.length,
      itemBuilder: (context, index) {
        final item = relatedItems[index];
        return _buildRelatedItemTile(item);
      },
    );
  }

  Widget _buildRelatedItemTile(TagRelatedItem item) {
    final isToGo = item.type == 'togo';

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: isToGo ? AppColors.success : AppColors.primary,
          child: Icon(
            isToGo ? Icons.place : Icons.check_circle,
            color: Colors.white,
            size: 20,
          ),
        ),
        title: Text(
          item.title,
          style: Theme.of(context).textTheme.titleSmall,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (item.status != null && item.status!.isNotEmpty)
              Text(
                item.status!,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            const SizedBox(height: 2),
            Row(
              children: [
                Text(
                  isToGo ? 'ToGo' : 'TODO',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: isToGo ? AppColors.success : AppColors.primary,
                        fontWeight: FontWeight.w500,
                      ),
                ),
                if (item.creatorName != null) ...[
                  const SizedBox(width: 8),
                  Text(
                    '• by ${item.creatorName}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.textSecondary,
                        ),
                  ),
                ],
              ],
            ),
          ],
        ),
        trailing: const Icon(Icons.chevron_right),
        onTap: () => _navigateToItem(item),
      ),
    );
  }
}
