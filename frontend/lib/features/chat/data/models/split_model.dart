import '../../../../core/services/time_service.dart';

/// Split participant model
class SplitParticipant {
  final String userId;
  final String userName;
  final double amount;

  const SplitParticipant({
    required this.userId,
    required this.userName,
    required this.amount,
  });

  factory SplitParticipant.fromJson(Map<String, dynamic> json) {
    return SplitParticipant(
      userId: json['userId'] as String? ?? '',
      userName: json['userName'] as String? ?? 'Unknown',
      amount: (json['amount'] as num?)?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'userName': userName,
      'amount': amount,
    };
  }

  SplitParticipant copyWith({
    String? userId,
    String? userName,
    double? amount,
  }) {
    return SplitParticipant(
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      amount: amount ?? this.amount,
    );
  }
}

/// Split type enum
enum SplitType {
  noSplit, // 不分攤（記錄費用但不分攤）
  equal, // 平均分攤
  custom, // 自定義金額
  percentage, // 按比例分攤
}

/// Split data model
class SplitData {
  final String? id; // Split ID from backend
  final String? messageId; // ID of the associated chat message
  final String title;
  final String? description;
  final String? category;
  final double totalAmount;
  final String currency;
  final String paidByUserId;
  final String paidByUserName;
  final SplitType splitType;
  final List<SplitParticipant> participants;
  final String creatorId;
  final DateTime createdAt;
  final bool isDeleted;

  const SplitData({
    this.id,
    this.messageId,
    required this.title,
    this.description,
    this.category,
    required this.totalAmount,
    this.currency = 'NT\$',
    required this.paidByUserId,
    required this.paidByUserName,
    this.splitType = SplitType.equal,
    required this.participants,
    required this.creatorId,
    required this.createdAt,
    this.isDeleted = false,
  });

  factory SplitData.fromJson(Map<String, dynamic> json) {
    return SplitData(
      id: json['id'] as String?,
      messageId: json['messageId'] as String?,
      title: json['title'] as String? ?? '',
      description: json['description'] as String?,
      category: json['category'] as String?,
      totalAmount: (json['totalAmount'] as num?)?.toDouble() ?? 0.0,
      currency: json['currency'] as String? ?? 'NT\$',
      paidByUserId: json['paidByUserId'] as String? ?? '',
      paidByUserName: json['paidByUserName'] as String? ?? 'Unknown',
      splitType: SplitType.values.firstWhere(
        (type) => type.name == (json['splitType'] as String? ?? 'equal'),
        orElse: () => SplitType.equal,
      ),
      participants: (json['participants'] as List<dynamic>?)
              ?.map((p) => SplitParticipant.fromJson(p as Map<String, dynamic>))
              .toList() ??
          [],
      creatorId: json['creatorId'] as String? ?? '',
      createdAt: json['createdAt'] != null
          ? TimeService.instance.parseDateTime(json['createdAt'] as String)
          : DateTime.now(),
      isDeleted: json['isDeleted'] as bool? ?? false,
    );
  }

  /// Create SplitData from backend API response (SplitResponseDto)
  factory SplitData.fromBackendJson(Map<String, dynamic> json) {
    return SplitData(
      id: json['id'] as String?,
      messageId: json['messageId'] as String?,
      title: json['title'] as String? ?? '',
      description: json['description'] as String?,
      category: json['category'] as String?,
      totalAmount: (json['totalAmount'] as num?)?.toDouble() ?? 0.0,
      currency: json['originalCurrency'] as String? ??
          'NT\$', // Use originalCurrency from backend
      paidByUserId: json['paidByUserId'] as String? ?? '',
      paidByUserName: json['paidByUserName'] as String? ?? 'Unknown',
      splitType: SplitType.values.firstWhere(
        (type) => type.name == (json['splitType'] as String? ?? 'equal'),
        orElse: () => SplitType.equal,
      ),
      participants: (json['participants'] as List<dynamic>?)
              ?.map((p) => SplitParticipant(
                    userId: p['userId'] as String? ?? '',
                    userName: p['userName'] as String? ?? 'Unknown',
                    amount: (p['amount'] as num?)?.toDouble() ?? 0.0,
                  ))
              .toList() ??
          [],
      creatorId: json['creatorId'] as String? ?? '',
      createdAt: json['createdAt'] != null
          ? TimeService.instance.parseDateTime(json['createdAt'] as String)
          : DateTime.now(),
      isDeleted: false, // Backend data is not deleted if we can retrieve it
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'messageId': messageId,
      'title': title,
      'description': description,
      'category': category,
      'totalAmount': totalAmount,
      'currency': currency,
      'paidByUserId': paidByUserId,
      'paidByUserName': paidByUserName,
      'splitType': splitType.name,
      'participants': participants.map((p) => p.toJson()).toList(),
      'creatorId': creatorId,
      'createdAt': createdAt.toIso8601String(),
      'isDeleted': isDeleted,
    };
  }

  // Helper methods
  double get participantsTotal =>
      participants.fold(0.0, (sum, p) => sum + p.amount);

  String get formattedAmount => '$currency${totalAmount.toStringAsFixed(2)}';
}

/// Create split request model
class CreateSplitRequest {
  final String title;
  final String? description;
  final String? category;
  final double totalAmount;
  final String currency;
  final String paidByUserId;
  final SplitType splitType;
  final List<String> participantUserIds;
  final Map<String, double>? customAmounts; // For custom split type
  final DateTime? date; // Optional date for the split

  const CreateSplitRequest({
    required this.title,
    this.description,
    this.category,
    required this.totalAmount,
    this.currency = 'NT\$',
    required this.paidByUserId,
    this.splitType = SplitType.equal,
    required this.participantUserIds,
    this.customAmounts,
    this.date,
  });

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'category': category,
      'totalAmount': totalAmount,
      'currency': currency,
      'paidByUserId': paidByUserId,
      'splitType': splitType.name,
      'participantUserIds': participantUserIds,
      'customAmounts': customAmounts,
      'date': date?.toIso8601String(),
    };
  }
}
