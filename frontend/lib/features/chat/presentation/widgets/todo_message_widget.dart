import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../spaces/presentation/widgets/space_theme_provider.dart';
import '../../data/models/message_model.dart';
import '../../data/models/todo_model.dart';

class TodoMessageWidget extends StatefulWidget {
  final MessageModel message;
  final SpaceThemeProvider spaceTheme;

  const TodoMessageWidget({
    super.key,
    required this.message,
    required this.spaceTheme,
  });

  @override
  State<TodoMessageWidget> createState() => _TodoMessageWidgetState();
}

class _TodoMessageWidgetState extends State<TodoMessageWidget> {
  late TodoData todoData;
  bool _isDeleted = false;

  @override
  void initState() {
    super.initState();
    _parseTodoData();
    // Check if message is deleted
    if (widget.message.isDeleted) {
      _isDeleted = true;
    }
  }

  @override
  void didUpdateWidget(TodoMessageWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.message.metadata != widget.message.metadata) {
      _parseTodoData();
    }
  }

  void _parseTodoData() {
    try {
      final metadata = widget.message.metadata;
      if (metadata != null) {
        todoData = TodoData.fromJson(metadata);
      } else {
        throw Exception('No metadata');
      }
    } catch (e) {
      // Fallback data if parsing fails
      todoData = TodoData(
        id: '', // Will be set when TODO is created
        spaceId: widget.message.spaceId,
        title:
            widget.message.content.isNotEmpty ? widget.message.content : 'TODO',
        participants: [],

        creatorId: widget.message.senderId,
        createdAt: widget.message.createdAt,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    // If TODO is deleted, show a simplified deleted state
    if (_isDeleted) {
      return Container(
        constraints: const BoxConstraints(maxWidth: 320),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Colors.grey[300]!,
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.check_box_outlined,
                  color: Colors.grey[400],
                  size: 16,
                ),
                const SizedBox(width: 6),
                Text(
                  'TODO',
                  style: TextStyle(
                    color: Colors.grey[400],
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                ),
                const Spacer(),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'Deleted',
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              todoData.title,
              style: TextStyle(
                color: Colors.grey[500],
                fontWeight: FontWeight.w600,
                fontSize: 15,
                decoration: TextDecoration.lineThrough,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'This TODO has been deleted',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[400],
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      constraints: const BoxConstraints(maxWidth: 320),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.border,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 8),
          _buildTitle(),
          if (todoData.details != null && todoData.details!.isNotEmpty) ...[
            const SizedBox(height: 6),
            _buildContent(),
          ],
          if (todoData.dueDate != null) ...[
            const SizedBox(height: 6),
            _buildDueDate(),
          ],
          if (todoData.location != null) ...[
            const SizedBox(height: 6),
            _buildLocation(),
          ],
          // Always show participants count
          const SizedBox(height: 6),
          _buildParticipantsCount(),
          if (todoData.tags.isNotEmpty) ...[
            const SizedBox(height: 6),
            _buildTags(),
          ],
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        const Icon(
          Icons.check_box_outlined,
          color: AppColors.primary,
          size: 16,
        ),
        const SizedBox(width: 6),
        const Text(
          'TODO',
          style: TextStyle(
            color: AppColors.primary,
            fontWeight: FontWeight.w600,
            fontSize: 12,
          ),
        ),
        const SizedBox(width: 8),
        const Spacer(),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          decoration: BoxDecoration(
            color: _getStatusColor().withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            _getStatusText(),
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: _getStatusColor(),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTitle() {
    return Text(
      todoData.title,
      style: TextStyle(
        color: todoData.status == TodoStatus.completed
            ? AppColors.textSecondary
            : AppColors.textPrimary,
        fontWeight: FontWeight.w600,
        fontSize: 15,
        decoration: todoData.status == TodoStatus.completed
            ? TextDecoration.lineThrough
            : null,
      ),
    );
  }

  Widget _buildContent() {
    if (todoData.details == null || todoData.details!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Text(
      todoData.details!,
      style: TextStyle(
        fontSize: 13,
        color: AppColors.textSecondary,
        decoration: todoData.status == TodoStatus.completed
            ? TextDecoration.lineThrough
            : null,
      ),
    );
  }

  Widget _buildDueDate() {
    final now = DateTime.now();
    final dueDate = todoData.dueDate!;
    final isOverdue =
        dueDate.isBefore(now) && todoData.status != TodoStatus.completed;
    final isToday = dueDate.year == now.year &&
        dueDate.month == now.month &&
        dueDate.day == now.day;

    // Format date and add time if available
    String displayText = _formatDueDate(dueDate);
    if (todoData.dueTime != null && todoData.dueTime!.isNotEmpty) {
      displayText += ' ${todoData.dueTime}';
    }

    return Row(
      children: [
        Icon(
          Icons.schedule,
          size: 14,
          color: isOverdue ? Colors.red : AppColors.textSecondary,
        ),
        const SizedBox(width: 4),
        Text(
          displayText,
          style: TextStyle(
            fontSize: 11,
            color: isOverdue ? Colors.red : AppColors.textSecondary,
            fontWeight: isToday ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
        if (isOverdue) ...[
          const SizedBox(width: 4),
          const Text(
            '(Overdue)',
            style: TextStyle(
              fontSize: 11,
              color: Colors.red,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildLocation() {
    return Row(
      children: [
        const Icon(
          Icons.location_on,
          size: 14,
          color: AppColors.textSecondary,
        ),
        const SizedBox(width: 4),
        Expanded(
          child: Text(
            todoData.location!,
            style: const TextStyle(
              fontSize: 11,
              color: AppColors.textSecondary,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildTags() {
    return Wrap(
      spacing: 4,
      runSpacing: 4,
      children: todoData.tags.map((tag) {
        final tagColor = Color(int.parse(tag.color.replaceFirst('#', '0xFF')));

        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          decoration: BoxDecoration(
            color: tagColor,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            tag.name,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildParticipantsCount() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        const Icon(
          Icons.people,
          size: 14,
          color: AppColors.textSecondary,
        ),
        const SizedBox(width: 4),
        Text(
          '${todoData.participants.length} 位參與者',
          style: const TextStyle(
            fontSize: 12,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  String _getStatusText() {
    switch (todoData.status) {
      case TodoStatus.pending:
        return 'Pending';
      case TodoStatus.inProgress:
        return 'In Progress';
      case TodoStatus.completed:
        return 'Completed';
      case TodoStatus.cancelled:
        return 'Cancelled';
    }
  }

  Color _getStatusColor() {
    switch (todoData.status) {
      case TodoStatus.pending:
        return Colors.orange;
      case TodoStatus.inProgress:
        return Colors.blue;
      case TodoStatus.completed:
        return Colors.green;
      case TodoStatus.cancelled:
        return Colors.red;
    }
  }

  String _formatDueDate(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));
    final dateOnly = DateTime(date.year, date.month, date.day);

    if (dateOnly == today) {
      return 'Today';
    } else if (dateOnly == tomorrow) {
      return 'Tomorrow';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
