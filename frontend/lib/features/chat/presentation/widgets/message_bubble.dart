import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../auth/presentation/bloc/auth_bloc.dart';
import '../../../spaces/presentation/widgets/space_theme_provider.dart';
import '../../data/models/message_model.dart';
import 'poll_message_widget.dart';
import 'dice_message_widget.dart';
import 'lucky_draw_message_widget.dart';
import 'split_message_widget.dart';
import 'settle_message_widget.dart';
import 'todo_message_widget.dart';
import 'togo_message_widget.dart';
import 'location_map_thumbnail.dart';

class MessageBubble extends StatelessWidget {
  final MessageModel message;
  final SpaceThemeProvider spaceTheme;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onReply;

  const MessageBubble({
    super.key,
    required this.message,
    required this.spaceTheme,
    this.onEdit,
    this.onDelete,
    this.onReply,
  });

  @override
  Widget build(BuildContext context) {
    // Use BlocBuilder to listen to AuthBloc state changes
    return BlocBuilder<AuthBloc, AuthState>(
      // Only rebuild when user ID changes, not on loading states
      buildWhen: (previous, current) {
        if (previous is AuthAuthenticated && current is AuthAuthenticated) {
          return previous.user['id'] != current.user['id'];
        }
        return previous.runtimeType != current.runtimeType;
      },
      builder: (context, authState) {
        String? currentUserId;
        if (authState is AuthAuthenticated) {
          currentUserId = authState.user['id'] as String?;
        }

        final isMe = message.senderId == currentUserId;

        return _buildMessageBubble(context, isMe);
      },
    );
  }

  Widget _buildMessageBubble(BuildContext context, bool isMe) {
    return Padding(
      padding: EdgeInsets.only(
        bottom:
            message.isDeleted ? 3 : 6, // Smaller padding for deleted messages
      ),
      child: Row(
        mainAxisAlignment:
            isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (!isMe && !message.isDeleted) ...[
            _buildAvatar(context, isMe),
            const SizedBox(width: 6),
          ],
          Flexible(
            child: Container(
              constraints: BoxConstraints(
                maxWidth: message.isDeleted
                    ? MediaQuery.of(context).size.width *
                        0.4 // Smaller width for deleted
                    : MediaQuery.of(context).size.width * 0.65,
              ),
              child: _buildMessageContent(context, isMe),
            ),
          ),
          if (isMe && !message.isDeleted) ...[
            const SizedBox(width: 4),
            _buildAvatar(context, isMe),
          ],
        ],
      ),
    );
  }

  Widget _buildAvatar(BuildContext context, bool isMe) {
    return CircleAvatar(
      radius: 14, // Reduced from 16 to 14
      backgroundColor: isMe ? AppColors.primary : Colors.grey[400],
      backgroundImage: message.senderAvatar != null
          ? NetworkImage(message.senderAvatar!)
          : null,
      child: message.senderAvatar == null
          ? Text(
              message.senderName.isNotEmpty
                  ? message.senderName[0].toUpperCase()
                  : '?',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 10, // Reduced from 12 to 10
                fontWeight: FontWeight.bold,
              ),
            )
          : null,
    );
  }

  Widget _buildMessageContent(BuildContext context, bool isMe) {
    return GestureDetector(
      onLongPress:
          message.isDeleted ? null : () => _showMessageOptions(context, isMe),
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: message.isDeleted ? 8 : _getMessagePadding().horizontal,
          vertical: message.isDeleted ? 4 : _getMessagePadding().vertical,
        ),
        decoration: BoxDecoration(
          color: message.isDeleted
              ? Colors.grey.withValues(alpha: 0.1)
              : _getMessageBackgroundColor(isMe),
          borderRadius: BorderRadius.only(
            topLeft: const Radius.circular(16),
            topRight: const Radius.circular(16),
            bottomLeft: Radius.circular(isMe ? 16 : 4),
            bottomRight: Radius.circular(isMe ? 4 : 16),
          ),
          border: message.isDeleted
              ? Border.all(color: Colors.grey.withValues(alpha: 0.3), width: 1)
              : _getMessageBorder(),
          boxShadow: message.isDeleted
              ? null
              : [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
        ),
        child: message.isDeleted
            ? _buildMessageBody(
                context, isMe) // Only show deleted message content
            : Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Sender name (only for received messages)
                  if (!isMe) ...[
                    Text(
                      message.senderName,
                      style: const TextStyle(
                        fontSize: 11, // Reduced from 12 to 11
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                    ),
                    const SizedBox(height: 2),
                  ],

                  // Reply indicator (if this is a reply)
                  if (message.replyTo != null) ...[
                    _buildReplyIndicator(context, isMe),
                    const SizedBox(height: 4), // Reduced from 6 to 4
                  ],

                  // Message content
                  _buildMessageBody(context, isMe),

                  const SizedBox(height: 2),

                  // Message metadata (time, edited indicator)
                  _buildMessageMetadata(context, isMe),
                ],
              ),
      ),
    );
  }

  Widget _buildReplyIndicator(BuildContext context, bool isMe) {
    final replyTo = message.replyTo!;

    return Container(
      padding: const EdgeInsets.all(6), // Reduced from 8 to 6
      decoration: BoxDecoration(
        color: (isMe ? Colors.white : Colors.grey[100])?.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border(
          left: BorderSide(
            color: isMe ? Colors.white : AppColors.primary,
            width: 3,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            replyTo.senderName,
            style: TextStyle(
              fontSize: 11,
              fontWeight: FontWeight.bold,
              color: isMe
                  ? Colors.white.withValues(alpha: 0.8)
                  : AppColors.primary,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            replyTo.content,
            style: TextStyle(
              fontSize: 11,
              color: isMe
                  ? Colors.white.withValues(alpha: 0.7)
                  : AppColors.textSecondary,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildMessageBody(BuildContext context, bool isMe) {
    // Handle deleted messages with a simple text display
    if (message.isDeleted) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.remove_circle_outline,
            size: 14,
            color: Colors.grey[500],
          ),
          const SizedBox(width: 6),
          Text(
            'Message has been deleted',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[500],
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      );
    }

    switch (message.type) {
      case MessageType.text:
        return Text(
          message.content,
          style: TextStyle(
            color: isMe ? Colors.white : AppColors.textPrimary,
            fontSize: 13, // Reduced from 14 to 13
          ),
        );

      case MessageType.image:
        return _buildImageMessage(context, isMe);

      case MessageType.file:
        return _buildFileMessage(context, isMe);

      case MessageType.location:
        return _buildLocationMessage(context, isMe);

      case MessageType.poll:
        return _buildPollMessage(context, isMe);

      case MessageType.dice:
        return _buildDiceMessage(context, isMe);

      case MessageType.luckyDraw:
        return _buildLuckyDrawMessage(context, isMe);

      case MessageType.split:
        return _buildSplitMessage(context, isMe);

      case MessageType.settle:
        return _buildSettleMessage(context, isMe);

      case MessageType.todo:
        return _buildTodoMessage(context, isMe);

      case MessageType.togo:
        return _buildToGoMessage(context, isMe);

      case MessageType.system:
        return _buildSystemMessage(context);
    }
  }

  Widget _buildImageMessage(BuildContext context, bool isMe) {
    final imageUrl = message.content;

    return GestureDetector(
      onTap: () => _showImageFullScreen(context, imageUrl),
      child: Container(
        constraints: const BoxConstraints(
          maxWidth: 220, // Reduced from 250 to 220
          maxHeight: 250, // Reduced from 300 to 250
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: CachedNetworkImage(
            imageUrl: imageUrl,
            fit: BoxFit.cover,
            placeholder: (context, url) => Container(
              height: 150,
              color: Colors.grey[300],
              child: const Center(
                child: CircularProgressIndicator(),
              ),
            ),
            errorWidget: (context, url, error) => Container(
              height: 150,
              color: Colors.grey[300],
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.broken_image, size: 48, color: Colors.grey),
                  const SizedBox(height: 8),
                  Text(
                    'Image failed to load',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Tap to retry',
                    style: TextStyle(
                      fontSize: 10,
                      color: Colors.grey[500],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFileMessage(BuildContext context, bool isMe) {
    final fileName = message.metadata?['fileName'] ?? 'Unknown file';
    final fileUrl = message.content;

    return GestureDetector(
      onTap: () => _downloadFile(context, fileUrl, fileName),
      child: Container(
        padding: const EdgeInsets.all(8), // Reduced from 12 to 8
        decoration: BoxDecoration(
          color:
              (isMe ? Colors.white : Colors.grey[100])?.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isMe
                ? Colors.white.withValues(alpha: 0.3)
                : Colors.grey.withValues(alpha: 0.3),
          ),
        ),
        child: Row(
          children: [
            Icon(
              _getFileIcon(fileName),
              color: isMe ? Colors.white : AppColors.primary,
              size: 24,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    fileName,
                    style: TextStyle(
                      color: isMe ? Colors.white : AppColors.textPrimary,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 2),
                  Text(
                    'Tap to download',
                    style: TextStyle(
                      color: (isMe ? Colors.white : AppColors.textSecondary)
                          .withValues(alpha: 0.7),
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.download,
              color: isMe
                  ? Colors.white.withValues(alpha: 0.7)
                  : AppColors.primary.withValues(alpha: 0.7),
              size: 20,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationMessage(BuildContext context, bool isMe) {
    final metadata = message.metadata;
    final latitude = metadata?['latitude']?.toDouble();
    final longitude = metadata?['longitude']?.toDouble();
    final address = metadata?['address'] as String?;
    final name = metadata?['name'] as String?;

    return Container(
      constraints: const BoxConstraints(maxWidth: 280),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Location icon and title
          Row(
            children: [
              Icon(
                Icons.location_on,
                color: isMe ? Colors.white : AppColors.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  name ?? 'Location',
                  style: TextStyle(
                    color: isMe ? Colors.white : AppColors.textPrimary,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),

          if (address != null) ...[
            const SizedBox(height: 4),
            Text(
              address,
              style: TextStyle(
                color: isMe
                    ? Colors.white.withValues(alpha: 0.9)
                    : AppColors.textSecondary,
                fontSize: 12,
              ),
            ),
          ],

          if (latitude != null && longitude != null) ...[
            const SizedBox(height: 6),
            // 真實的地圖縮圖
            LocationMapThumbnail(
              latitude: latitude,
              longitude: longitude,
              locationName: name,
              isMe: isMe,
              height: 100,
              onTap: () => _openLocationInMaps(latitude, longitude, name),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSystemMessage(BuildContext context) {
    return Text(
      message.content,
      style: const TextStyle(
        color: AppColors.textSecondary,
        fontSize: 12,
        fontStyle: FontStyle.italic,
      ),
    );
  }

  Widget _buildMessageMetadata(BuildContext context, bool isMe) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Edited indicator
        if (message.editedAt != null) ...[
          Text(
            'edited',
            style: TextStyle(
              fontSize: 10,
              color: isMe
                  ? Colors.white.withValues(alpha: 0.7)
                  : AppColors.textSecondary,
              fontStyle: FontStyle.italic,
            ),
          ),
          const SizedBox(width: 4),
        ],

        // Timestamp
        Text(
          _formatTime(message.createdAt),
          style: TextStyle(
            fontSize: 10,
            color: isMe
                ? Colors.white.withValues(alpha: 0.7)
                : AppColors.textSecondary,
          ),
        ),

        // Message status indicator (only for own messages)
        if (isMe) ...[
          const SizedBox(width: 4),
          _buildMessageStatusIcon(isMe),
        ],
      ],
    );
  }

  void _showMessageOptions(BuildContext context, bool isMe) {
    final options = <String>[];

    // Reply option is available for all messages
    options.add('Reply');

    // Copy option is available for text messages
    if (message.type == MessageType.text) {
      options.add('Copy');
    }

    // Edit and Delete options are only available for own messages
    if (isMe) {
      if (message.type == MessageType.text) {
        options.add('Edit');
      }
      options.add('Delete');
    }

    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.symmetric(vertical: 20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: options.map((option) {
            IconData icon;
            Color? color;

            switch (option) {
              case 'Reply':
                icon = Icons.reply;
                break;
              case 'Copy':
                icon = Icons.copy;
                break;
              case 'Edit':
                icon = Icons.edit;
                break;
              case 'Delete':
                icon = Icons.delete;
                color = Colors.red;
                break;
              default:
                icon = Icons.more_horiz;
            }

            return ListTile(
              leading: Icon(icon, color: color),
              title: Text(
                option,
                style: TextStyle(color: color),
              ),
              onTap: () {
                Navigator.pop(context);
                _handleOptionSelected(context, option);
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  void _handleOptionSelected(BuildContext context, String option) {
    switch (option) {
      case 'Reply':
        onReply?.call();
        break;
      case 'Copy':
        _copyToClipboard(context);
        break;
      case 'Edit':
        onEdit?.call();
        break;
      case 'Delete':
        onDelete?.call();
        break;
    }
  }

  void _copyToClipboard(BuildContext context) async {
    await Clipboard.setData(ClipboardData(text: message.content));
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Message copied to clipboard')),
      );
    }
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final messageDate = DateTime(dateTime.year, dateTime.month, dateTime.day);

    if (messageDate == today) {
      // Today - show time only
      final hour = dateTime.hour.toString().padLeft(2, '0');
      final minute = dateTime.minute.toString().padLeft(2, '0');
      return '$hour:$minute';
    } else {
      // Other days - show date and time
      final month = dateTime.month.toString().padLeft(2, '0');
      final day = dateTime.day.toString().padLeft(2, '0');
      final hour = dateTime.hour.toString().padLeft(2, '0');
      final minute = dateTime.minute.toString().padLeft(2, '0');
      return '$month/$day $hour:$minute';
    }
  }

  void _showImageFullScreen(BuildContext context, String imageUrl) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => Scaffold(
          backgroundColor: Colors.black,
          appBar: AppBar(
            backgroundColor: Colors.transparent,
            iconTheme: const IconThemeData(color: Colors.white),
          ),
          body: Center(
            child: InteractiveViewer(
              child: CachedNetworkImage(
                imageUrl: imageUrl,
                fit: BoxFit.contain,
                placeholder: (context, url) => const Center(
                  child: CircularProgressIndicator(color: Colors.white),
                ),
                errorWidget: (context, url, error) => const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.broken_image, color: Colors.white, size: 64),
                      SizedBox(height: 16),
                      Text(
                        'Image failed to load',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: 8),
                      Text(
                        'Check your network connection',
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _downloadFile(
      BuildContext context, String fileUrl, String fileName) async {
    try {
      final uri = Uri.parse(fileUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        throw 'Could not launch $fileUrl';
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to download file: $e')),
        );
      }
    }
  }

  IconData _getFileIcon(String fileName) {
    final extension = fileName.toLowerCase().split('.').last;

    switch (extension) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Icons.description;
      case 'xls':
      case 'xlsx':
        return Icons.table_chart;
      case 'ppt':
      case 'pptx':
        return Icons.slideshow;
      case 'zip':
      case 'rar':
      case '7z':
        return Icons.archive;
      case 'mp3':
      case 'wav':
      case 'flac':
        return Icons.audio_file;
      case 'mp4':
      case 'avi':
      case 'mov':
        return Icons.video_file;
      case 'txt':
        return Icons.text_snippet;
      default:
        return Icons.insert_drive_file;
    }
  }

  Widget _buildMessageStatusIcon(bool isMe) {
    IconData iconData;
    Color iconColor;

    switch (message.status) {
      case MessageStatus.sending:
        iconData = Icons.access_time;
        iconColor = isMe ? Colors.white.withValues(alpha: 0.7) : Colors.grey;
        break;
      case MessageStatus.sent:
        iconData = Icons.check;
        iconColor = isMe ? Colors.white.withValues(alpha: 0.7) : Colors.grey;
        break;
      case MessageStatus.delivered:
        iconData = Icons.done_all;
        iconColor = isMe ? Colors.white.withValues(alpha: 0.7) : Colors.grey;
        break;
      case MessageStatus.read:
        iconData = Icons.done_all;
        iconColor = isMe ? Colors.blue : Colors.blue;
        break;
      case MessageStatus.failed:
        iconData = Icons.error_outline;
        iconColor = Colors.red;
        break;
    }

    return Icon(
      iconData,
      size: 12,
      color: iconColor,
    );
  }

  Future<void> _openLocationInMaps(
      double latitude, double longitude, String? name) async {
    try {
      // Try different URL schemes in order of preference
      final urls = [
        // Google Maps app URL scheme (Android)
        'geo:$latitude,$longitude?q=$latitude,$longitude',
        // Google Maps web URL (fallback)
        'https://maps.google.com/?q=$latitude,$longitude',
        // Apple Maps (iOS)
        'http://maps.apple.com/?ll=$latitude,$longitude',
        // Generic maps URL
        'https://www.google.com/maps/search/?api=1&query=$latitude,$longitude',
      ];

      bool launched = false;
      for (final url in urls) {
        try {
          final uri = Uri.parse(url);
          if (await canLaunchUrl(uri)) {
            await launchUrl(uri, mode: LaunchMode.externalApplication);
            launched = true;
            break;
          }
        } catch (e) {
          developer.log('Failed to launch URL $url: $e', name: 'MessageBubble');
          continue;
        }
      }

      if (!launched) {
        developer.log('No suitable map application found',
            name: 'MessageBubble');
        // Could show a snackbar to user here if needed
      }
    } catch (e) {
      developer.log('Failed to open location in maps: $e',
          name: 'MessageBubble');
    }
  }

  Widget _buildPollMessage(BuildContext context, bool isMe) {
    return PollMessageWidget(
      message: message,
      spaceTheme: spaceTheme,
      onVote: (messageId, optionIds) async {
        // Vote handling is done internally by PollMessageWidget
        // No notification needed
      },
    );
  }

  Widget _buildDiceMessage(BuildContext context, bool isMe) {
    return DiceMessageWidget(
      message: message,
      spaceTheme: spaceTheme,
      onRoll: (messageId, diceData) async {
        // Dice roll handling is done internally by DiceMessageWidget
        // No notification needed
      },
    );
  }

  Widget _buildLuckyDrawMessage(BuildContext context, bool isMe) {
    return LuckyDrawMessageWidget(
      message: message,
      spaceTheme: spaceTheme,
      onJoin: (messageId, luckyDrawData) async {
        // Join handling is done internally by LuckyDrawMessageWidget
        // No notification needed
      },
      onDraw: (messageId, luckyDrawData) async {
        // Draw handling is done internally by LuckyDrawMessageWidget
        // No notification needed
      },
    );
  }

  Widget _buildSplitMessage(BuildContext context, bool isMe) {
    return SplitMessageWidget(
      message: message,
      spaceTheme: spaceTheme,
    );
  }

  Widget _buildSettleMessage(BuildContext context, bool isMe) {
    return SettleMessageWidget(
      message: message,
      spaceTheme: spaceTheme,
    );
  }

  Widget _buildTodoMessage(BuildContext context, bool isMe) {
    return TodoMessageWidget(
      message: message,
      spaceTheme: spaceTheme,
    );
  }

  Widget _buildToGoMessage(BuildContext context, bool isMe) {
    return ToGoMessageWidget(
      message: message,
      spaceTheme: spaceTheme,
    );
  }

  Color _getMessageBackgroundColor(bool isMe) {
    return isMe ? AppColors.primary : Colors.white;
  }

  Border? _getMessageBorder() {
    return null;
  }

  ({double horizontal, double vertical}) _getMessagePadding() {
    return (horizontal: 10.0, vertical: 6.0);
  }
}
