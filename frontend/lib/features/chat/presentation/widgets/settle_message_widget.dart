import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../spaces/presentation/widgets/space_theme_provider.dart';
import '../../data/models/message_model.dart';
import '../../data/models/settle_model.dart';

class SettleMessageWidget extends StatefulWidget {
  final MessageModel message;
  final SpaceThemeProvider spaceTheme;

  const SettleMessageWidget({
    super.key,
    required this.message,
    required this.spaceTheme,
  });

  @override
  State<SettleMessageWidget> createState() => _SettleMessageWidgetState();
}

class _SettleMessageWidgetState extends State<SettleMessageWidget> {
  late SettleData settleData;
  bool _isDeleted = false;

  @override
  void initState() {
    super.initState();
    _parseSettleData();
    // Check if message is deleted
    if (widget.message.isDeleted) {
      _isDeleted = true;
    }
  }

  @override
  void didUpdateWidget(SettleMessageWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.message.metadata != widget.message.metadata) {
      _parseSettleData();
    }
  }

  void _parseSettleData() {
    try {
      final metadata = widget.message.metadata;
      if (metadata != null) {
        settleData = SettleData.fromJson(metadata);
      } else {
        throw Exception('No metadata');
      }
    } catch (e) {
      // Fallback data if parsing fails
      settleData = SettleData(
        fromUserId: '',
        fromUserName: 'Unknown',
        toUserId: '',
        toUserName: 'Unknown',
        amount: 0.0,
        currency: 'USD',
        description: 'Payment',
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    // Build the normal card structure, but modify content if deleted
    return Container(
      constraints: const BoxConstraints(maxWidth: 320),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.border,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 8),
          _isDeleted ? _buildDeletedContent() : _buildPaymentDetails(),
        ],
      ),
    );
  }

  Widget _buildDeletedContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '${settleData.fromUserName} → ${settleData.toUserName}',
          style: TextStyle(
            color: Colors.grey[500],
            fontWeight: FontWeight.w600,
            fontSize: 15,
            decoration: TextDecoration.lineThrough,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          'This payment has been deleted',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[400],
            fontStyle: FontStyle.italic,
          ),
        ),
      ],
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        const Icon(
          Icons.account_balance_wallet,
          color: AppColors.primary,
          size: 16,
        ),
        const SizedBox(width: 6),
        const Text(
          'Settle',
          style: TextStyle(
            color: AppColors.primary,
            fontWeight: FontWeight.w600,
            fontSize: 12,
          ),
        ),
        if (_isDeleted) ...[
          const Spacer(),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              'Deleted',
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildPaymentDetails() {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: AppColors.surfaceVariant,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Payment direction - more prominent with boxes
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // From user box
              Container(
                padding:
                    const EdgeInsets.symmetric(vertical: 8, horizontal: 20),
                decoration: BoxDecoration(
                  color: AppColors.surfaceVariant,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  settleData.fromUserName,
                  style: const TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                    color: AppColors.primary,
                  ),
                ),
              ),

              // Arrow
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 12),
                child: Icon(
                  Icons.arrow_forward,
                  color: AppColors.primary,
                  size: 20,
                ),
              ),

              // To user box
              Container(
                padding:
                    const EdgeInsets.symmetric(vertical: 8, horizontal: 20),
                decoration: BoxDecoration(
                  color: AppColors.surfaceVariant,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  settleData.toUserName,
                  style: const TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                    color: AppColors.primary,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          // Amount display
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Payment Amount',
                      style: TextStyle(
                        fontSize: 11,
                        color: AppColors.textSecondary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      '${settleData.currency} ${settleData.amount.toStringAsFixed(2)}',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),

          // Only show custom description if it's not the default "Payment from X to Y"
          if (settleData.description != null &&
              settleData.description!.isNotEmpty &&
              !settleData.description!.startsWith('Payment from')) ...[
            const SizedBox(height: 8),
            Text(
              settleData.description!,
              style: const TextStyle(
                fontSize: 12,
                color: AppColors.textSecondary,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
