import 'package:flutter/material.dart';
import 'dart:io';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/widgets/tag_selector.dart';
import '../../../../shared/widgets/custom_text_field.dart';
import '../../data/models/togo_model.dart';
import '../../../chat/domain/services/google_location_service.dart';

class CreateToGoDialog extends StatefulWidget {
  final String spaceId;
  final ToGoData? existingTogo;
  final Function(CreateToGoRequest) onCreated;

  const CreateToGoDialog({
    super.key,
    required this.spaceId,
    this.existingTogo,
    required this.onCreated,
  });

  @override
  State<CreateToGoDialog> createState() => _CreateToGoDialogState();
}

class _CreateToGoDialogState extends State<CreateToGoDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _addressController = TextEditingController();
  final _linkController = TextEditingController();

  List<String> _selectedTags = [];
  double? _latitude;
  double? _longitude;
  String? _originalGoogleMapsLink;

  bool get _isEditing => widget.existingTogo != null;

  @override
  void initState() {
    super.initState();
    if (_isEditing) {
      _initializeWithExistingData();
    }
  }

  void _initializeWithExistingData() {
    final togo = widget.existingTogo!;
    _nameController.text = togo.name;
    _descriptionController.text = togo.description ?? '';
    _addressController.text = togo.address ?? '';
    // Initialize with existing tags from the ToGo
    _selectedTags = togo.tags.map((tag) => tag.name).toList();
    _latitude = togo.latitude;
    _longitude = togo.longitude;
    _originalGoogleMapsLink = togo.originalLink;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _addressController.dispose();
    _linkController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: DraggableScrollableSheet(
          initialChildSize: 0.9,
          minChildSize: 0.5,
          maxChildSize: 0.95,
          expand: false,
          builder: (context, scrollController) {
            return Column(
              children: [
                _buildDragHandle(),
                _buildHeader(),
                Expanded(
                  child: _buildContent(scrollController),
                ),
                _buildActions(),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildDragHandle() {
    return Container(
      margin: const EdgeInsets.only(top: 8, bottom: 8),
      width: 40,
      height: 4,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 12, 12, 8),
      child: Row(
        children: [
          Icon(
            _isEditing ? Icons.edit_location : Icons.add_location,
            color: AppColors.primary,
            size: 22,
          ),
          const SizedBox(width: 8),
          Text(
            _isEditing ? 'Edit ToGo' : 'Add ToGo',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
          ),
          const Spacer(),
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.close, size: 20),
            padding: const EdgeInsets.all(4),
            constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(ScrollController scrollController) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Form(
        key: _formKey,
        child: SingleChildScrollView(
          controller: scrollController,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildGoogleLinkInput(),
              const SizedBox(height: 12),
              _buildNameInput(),
              const SizedBox(height: 12),
              _buildDescriptionInput(),
              const SizedBox(height: 12),
              _buildAddressInput(),
              const SizedBox(height: 12),
              _buildTagsSelection(),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGoogleLinkInput() {
    return TextField(
      controller: _linkController,
      decoration: InputDecoration(
        labelText: 'Google Maps Link (Optional)',
        hintText: 'Paste Google Maps link to auto-fill details...',
        prefixIcon: const Icon(Icons.link, size: 20),
        suffixIcon: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (_linkController.text.isNotEmpty)
              IconButton(
                icon: const Icon(Icons.clear, size: 20),
                onPressed: () {
                  _linkController.clear();
                  setState(() {});
                },
              ),
            IconButton(
              icon: const Icon(Icons.auto_fix_high, size: 20),
              onPressed: () {
                if (_linkController.text.trim().isNotEmpty) {
                  _parseGoogleMapsLink(_linkController.text.trim());
                }
              },
              tooltip: 'Auto-fill from link',
            ),
          ],
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        filled: true,
        fillColor: Colors.blue[50],
        contentPadding:
            const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
      ),
      style: const TextStyle(fontSize: 14),
      onChanged: (value) => setState(() {}),
      onSubmitted: (value) {
        if (value.trim().isNotEmpty) {
          _parseGoogleMapsLink(value.trim());
        }
      },
    );
  }

  Widget _buildNameInput() {
    return CustomTextField(
      controller: _nameController,
      label: 'ToGo Name',
      hintText: 'Enter ToGo name',
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'Please enter ToGo name';
        }
        return null;
      },
    );
  }

  Widget _buildDescriptionInput() {
    return CustomTextField(
      controller: _descriptionController,
      label: 'Description (Optional)',
      hintText: 'Describe this ToGo destination',
      maxLines: 3,
    );
  }

  Widget _buildAddressInput() {
    return CustomTextField(
      controller: _addressController,
      label: 'Address (Optional)',
      hintText: 'Enter address or location',
      suffixIcon: IconButton(
        icon: const Icon(Icons.my_location),
        onPressed: _pickLocation,
        tooltip: 'Select Location',
      ),
    );
  }

  Widget _buildTagsSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Tags',
          style: Theme.of(context).textTheme.titleSmall,
        ),
        const SizedBox(height: 8),
        TagSelector(
          spaceId: widget.spaceId,
          selectedTags: _selectedTags,
          onTagsChanged: (tags) {
            setState(() {
              _selectedTags = tags;
            });
          },
          hintText: 'Add tags for this ToGo...',
          allowCustomTags: true,
          maxTags: 10,
        ),
      ],
    );
  }

  Widget _buildActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: Colors.grey[200]!, width: 1),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: TextButton(
                onPressed: () => Navigator.pop(context),
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text('Cancel'),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              flex: 2,
              child: ElevatedButton(
                onPressed: _submit,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                ),
                child: Text(_isEditing ? 'Update ToGo' : 'Add ToGo'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _pickLocation() {
    // TODO: Implement location picker
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Location picker coming soon!')),
    );
  }

  /// 解析 Google Maps 分享連結
  Future<void> _parseGoogleMapsLink(String link) async {
    try {
      // 保存原始連結
      _originalGoogleMapsLink = link.trim();

      // 先清理 URL，移除所有參數
      final cleanUrl = _cleanGoogleMapsUrl(link);
      debugPrint('🔗 原始 URL: $link');
      debugPrint('🧹 清理後 URL: $cleanUrl');

      // 先嘗試提取座標，同時獲取展開後的 URL
      String urlForAddressExtraction = cleanUrl;

      // 如果是短網址，先展開獲取完整 URL
      if (cleanUrl.contains('goo.gl')) {
        debugPrint('🔗 檢測到短網址，嘗試展開...');
        final expandedUrl = await _expandShortUrl(cleanUrl);
        if (expandedUrl != null) {
          debugPrint('📍 展開後的 URL: $expandedUrl');
          urlForAddressExtraction = expandedUrl;
        }
      }

      // 嘗試從 URL 中提取座標
      final coordinates =
          await _extractCoordinatesFromGoogleMapsUrl(urlForAddressExtraction);

      if (coordinates != null) {
        debugPrint('✅ 成功提取座標: ${coordinates['lat']}, ${coordinates['lng']}');

        // 使用 Google API 進行反向地理編碼
        try {
          final googleLocation = await GoogleLocationService.reverseGeocode(
            coordinates['lat']!,
            coordinates['lng']!,
          );

          if (googleLocation != null && mounted) {
            setState(() {
              _nameController.text =
                  googleLocation.name ?? 'Location from Link';
              _addressController.text = googleLocation.formattedAddress;
              _latitude = coordinates['lat'];
              _longitude = coordinates['lng'];
            });
          }
        } catch (e) {
          debugPrint('❌ 反向地理編碼失敗: $e');
          if (mounted) {
            setState(() {
              _nameController.text = 'Location from Link';
              _addressController.text =
                  '${coordinates['lat']!.toStringAsFixed(6)}, ${coordinates['lng']!.toStringAsFixed(6)}';
              _latitude = coordinates['lat'];
              _longitude = coordinates['lng'];
            });
          }
        }

        // 清空連結輸入框
        _linkController.clear();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('✅ Google Maps link parsed successfully!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        // 無法直接提取座標，嘗試從 URL 中提取地址信息
        debugPrint('🔍 無法直接提取座標，嘗試從 URL 提取地址信息...');
        await _extractLocationInfoFromUrl(urlForAddressExtraction);
      }
    } catch (e) {
      debugPrint('❌ 解析 Google Maps 連結失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Parse failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _submit() {
    if (_formKey.currentState!.validate()) {
      final request = CreateToGoRequest(
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        address: _addressController.text.trim().isEmpty
            ? null
            : _addressController.text.trim(),
        latitude: _latitude,
        longitude: _longitude,
        originalLink: _originalGoogleMapsLink,
        tags: _selectedTags,
      );

      widget.onCreated(request);
      Navigator.of(context).pop();
    }
  }

  /// 清理 Google Maps URL，移除所有參數
  String _cleanGoogleMapsUrl(String url) {
    try {
      final uri = Uri.parse(url);

      // 如果是短網址 (goo.gl)，只保留基本路徑
      if (uri.host.contains('goo.gl')) {
        return '${uri.scheme}://${uri.host}${uri.path}';
      }

      // 對於其他 Google Maps URL，移除查詢參數
      return '${uri.scheme}://${uri.host}${uri.path}';
    } catch (e) {
      debugPrint('❌ URL 清理失敗: $e');
      return url; // 如果解析失敗，返回原始 URL
    }
  }

  /// 展開短網址
  Future<String?> _expandShortUrl(String shortUrl) async {
    try {
      // 使用 HTTP HEAD 請求獲取重定向 URL
      final client = HttpClient();
      client.autoUncompress = false;

      final request = await client.openUrl('HEAD', Uri.parse(shortUrl));
      request.followRedirects = false;

      final response = await request.close();

      if (response.statusCode == 301 || response.statusCode == 302) {
        final location = response.headers.value('location');
        client.close();
        return location;
      }

      client.close();
      return null;
    } catch (e) {
      debugPrint('❌ 短網址展開失敗: $e');
      return null;
    }
  }

  /// 從 Google Maps URL 中提取座標
  Future<Map<String, double>?> _extractCoordinatesFromGoogleMapsUrl(
      String url) async {
    try {
      // 檢查是否為短網址，需要先展開
      if (url.contains('goo.gl')) {
        debugPrint('🔗 檢測到短網址，嘗試展開...');
        final expandedUrl = await _expandShortUrl(url);
        if (expandedUrl != null) {
          debugPrint('📍 展開後的 URL: $expandedUrl');
          return _extractCoordinatesFromExpandedUrl(expandedUrl);
        }
      }

      // 直接解析完整 URL
      return _extractCoordinatesFromExpandedUrl(url);
    } catch (e) {
      debugPrint('❌ 座標提取失敗: $e');
      return null;
    }
  }

  /// 從展開的 URL 中提取座標
  Map<String, double>? _extractCoordinatesFromExpandedUrl(String url) {
    try {
      // 格式1: @緯度,經度,縮放級別z
      final regex1 = RegExp(r'@(-?\d+\.?\d*),(-?\d+\.?\d*),\d+\.?\d*z');
      final match1 = regex1.firstMatch(url);

      if (match1 != null) {
        final lat = double.parse(match1.group(1)!);
        final lng = double.parse(match1.group(2)!);
        debugPrint('✅ 格式1 匹配: lat=$lat, lng=$lng');
        return {'lat': lat, 'lng': lng};
      }

      // 格式2: !3d緯度!4d經度
      final regex2 = RegExp(r'!3d(-?\d+\.?\d*)!4d(-?\d+\.?\d*)');
      final match2 = regex2.firstMatch(url);

      if (match2 != null) {
        final lat = double.parse(match2.group(1)!);
        final lng = double.parse(match2.group(2)!);
        debugPrint('✅ 格式2 匹配: lat=$lat, lng=$lng');
        return {'lat': lat, 'lng': lng};
      }

      // 格式3: /place/地點名稱/@緯度,經度
      final regex3 = RegExp(r'/place/[^/]+/@(-?\d+\.?\d*),(-?\d+\.?\d*)');
      final match3 = regex3.firstMatch(url);

      if (match3 != null) {
        final lat = double.parse(match3.group(1)!);
        final lng = double.parse(match3.group(2)!);
        debugPrint('✅ 格式3 匹配: lat=$lat, lng=$lng');
        return {'lat': lat, 'lng': lng};
      }

      debugPrint('❌ 無法匹配任何已知格式');
      return null;
    } catch (e) {
      debugPrint('❌ 座標提取失敗: $e');
      return null;
    }
  }

  /// 從 URL 中提取地址信息並進行地理編碼
  Future<void> _extractLocationInfoFromUrl(String url) async {
    debugPrint('🔍 開始提取地址信息，URL: $url');
    try {
      final uri = Uri.parse(url);
      final qParam = uri.queryParameters['q'];

      if (qParam != null) {
        String decodedQuery;
        try {
          decodedQuery = Uri.decodeComponent(qParam);
        } catch (e) {
          // 如果解碼失敗，嘗試手動替換常見的編碼
          debugPrint('⚠️ URI 解碼失敗，嘗試手動解碼: $e');
          decodedQuery = qParam
              .replaceAll('+', ' ')
              .replaceAll('%E8%99%9F', '號')
              .replaceAll('%2C', ',')
              .replaceAll('%20', ' ');
        }
        debugPrint('🔍 從 URL 提取的地址: $decodedQuery');

        if (mounted) {
          setState(() {
            _nameController.text = decodedQuery;
            _addressController.text = decodedQuery;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('✅ Extracted location from link: $decodedQuery'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('❌ No address information in link'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      debugPrint('❌ URL 地址信息提取失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Parse failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
