import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../data/models/togo_model.dart';

class ToGoCard extends StatelessWidget {
  final ToGoData togo;
  final String spaceId;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;

  const ToGoCard({
    super.key,
    required this.togo,
    required this.spaceId,
    this.onTap,
    this.onEdit,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        onLongPress: () => _showContextMenu(context),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with name and status
              Row(
                children: [
                  Expanded(
                    child: Text(
                      togo.name,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                  ),
                  const Icon(
                    Icons.place,
                    color: AppColors.primary,
                    size: 20,
                  ),
                ],
              ),

              // Description
              if (togo.description != null && togo.description!.isNotEmpty) ...[
                const SizedBox(height: 8),
                Text(
                  togo.description!,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],

              // Location
              if (togo.address != null || togo.hasLocation) ...[
                const SizedBox(height: 8),
                GestureDetector(
                  onTap: () => _openInGoogleMaps(togo),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.location_on,
                        size: 16,
                        color: AppColors.primary,
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          togo.displayLocation,
                          style:
                              Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: AppColors.primary,
                                  ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const Icon(
                        Icons.open_in_new,
                        size: 12,
                        color: AppColors.primary,
                      ),
                    ],
                  ),
                ),
              ],

              // Tags display
              if (togo.tags.isNotEmpty) ...[
                const SizedBox(height: 8),
                _buildTags(),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTags() {
    return Wrap(
      spacing: 4,
      runSpacing: 4,
      children: togo.tags.map((tag) {
        final tagColor = Color(int.parse(tag.color.replaceFirst('#', '0xFF')));

        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
          decoration: BoxDecoration(
            color: tagColor,
            borderRadius: BorderRadius.circular(10),
          ),
          child: Text(
            tag.name,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 9,
              fontWeight: FontWeight.w500,
            ),
          ),
        );
      }).toList(),
    );
  }

  void _showContextMenu(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.edit, color: AppColors.primary),
              title: const Text('Edit'),
              onTap: () {
                Navigator.pop(context);
                if (onEdit != null) onEdit!();
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete, color: AppColors.error),
              title: const Text('Delete'),
              onTap: () {
                Navigator.pop(context);
                if (onDelete != null) onDelete!();
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _openInGoogleMaps(ToGoData togo) async {
    try {
      String urlToOpen;
      if (togo.originalLink != null && togo.originalLink!.isNotEmpty) {
        // Use original Google Maps link
        urlToOpen = togo.originalLink!;
        debugPrint('🔗 Using original link: ${togo.originalLink}');
      } else if (togo.address != null && togo.address!.isNotEmpty) {
        // Fallback: use address to build search URL
        final encodedAddress = Uri.encodeComponent(togo.address!);
        urlToOpen =
            'https://www.google.com/maps/search/?api=1&query=$encodedAddress';
        debugPrint('🔍 Using address search: $urlToOpen');
      } else {
        debugPrint('❌ No address or original link available');
        return;
      }

      final uri = Uri.parse(urlToOpen);

      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
      } else {
        debugPrint('❌ Cannot open Google Maps: $urlToOpen');
      }
    } catch (e) {
      debugPrint('❌ Error opening Google Maps: $e');
    }
  }
}
