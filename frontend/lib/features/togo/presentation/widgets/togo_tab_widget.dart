import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/animations/enhanced_loading_animations.dart';
import '../../data/models/togo_model.dart';
import '../bloc/togo_bloc.dart';
import '../bloc/togo_event.dart';
import '../bloc/togo_state.dart';
import 'togo_card.dart';
import 'create_togo_dialog.dart';

class ToGoTabWidget extends StatefulWidget {
  final String spaceId;

  const ToGoTabWidget({
    super.key,
    required this.spaceId,
  });

  @override
  State<ToGoTabWidget> createState() => _ToGoTabWidgetState();
}

class _ToGoTabWidgetState extends State<ToGoTabWidget> {
  @override
  void initState() {
    super.initState();
    context.read<ToGoBloc>().add(ToGoLoadRequested(spaceId: widget.spaceId));
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ToGoBloc, ToGoState>(
      listener: (context, state) {
        if (state is ToGoOperationSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: AppColors.success,
            ),
          );
        } else if (state is ToGoError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: AppColors.error,
            ),
          );
        }
      },
      builder: (context, state) {
        if (state is ToGoLoading) {
          return const Center(
            child: EnhancedLoadingIndicator(
              type: LoadingAnimationType.pulse,
              color: AppColors.primary,
            ),
          );
        }

        if (state is ToGoError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  size: 64,
                  color: AppColors.error,
                ),
                const SizedBox(height: 16),
                Text(
                  state.message,
                  style: const TextStyle(color: AppColors.error),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: () {
                    context.read<ToGoBloc>().add(
                          ToGoLoadRequested(spaceId: widget.spaceId),
                        );
                  },
                  icon: const Icon(Icons.refresh),
                  label: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        if (state is ToGoLoaded) {
          return _buildLoadedContentWithFAB(context, state);
        }

        if (state is ToGoOperationInProgress) {
          return Stack(
            children: [
              _buildToGoList(state.togos),
              Container(
                color: Colors.black26,
                child: Center(
                  child: Card(
                    child: Padding(
                      padding:
                          const EdgeInsets.all(AppConstants.defaultPadding),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const EnhancedLoadingIndicator(
                            type: LoadingAnimationType.pulse,
                            color: AppColors.primary,
                          ),
                          const SizedBox(height: 16),
                          Text(state.operation),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          );
        }

        return const Center(
          child: EnhancedLoadingIndicator(
            type: LoadingAnimationType.pulse,
            color: AppColors.primary,
          ),
        );
      },
    );
  }

  Widget _buildLoadedContentWithFAB(BuildContext context, ToGoLoaded state) {
    return Stack(
      children: [
        _buildLoadedContent(context, state),
        Positioned(
          bottom: 16,
          right: 16,
          child: FloatingActionButton(
            onPressed: () => _showCreateDialog(context),
            child: const Icon(Icons.add),
          ),
        ),
      ],
    );
  }

  Widget _buildLoadedContent(BuildContext context, ToGoLoaded state) {
    return Column(
      children: [
        // Simple header with count
        Container(
          padding: const EdgeInsets.fromLTRB(
            AppConstants.defaultPadding,
            AppConstants.defaultPadding,
            AppConstants.defaultPadding,
            4,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${state.totalCount} Places',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
              ),
              // Sort button
              PopupMenuButton<ToGoSortOption>(
                icon: const Icon(Icons.sort),
                onSelected: (sortOption) {
                  context.read<ToGoBloc>().add(
                        ToGoSortChanged(sortOption: sortOption),
                      );
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: ToGoSortOption.createdAtDesc,
                    child: Text('Newest First'),
                  ),
                  const PopupMenuItem(
                    value: ToGoSortOption.createdAtAsc,
                    child: Text('Oldest First'),
                  ),
                  const PopupMenuItem(
                    value: ToGoSortOption.nameAsc,
                    child: Text('Name A-Z'),
                  ),
                  const PopupMenuItem(
                    value: ToGoSortOption.nameDesc,
                    child: Text('Name Z-A'),
                  ),
                ],
              ),
            ],
          ),
        ),

        // Content
        Expanded(
          child: state.filteredTogos.isEmpty
              ? _buildEmptyState(context, state)
              : _buildToGoList(state.filteredTogos),
        ),
      ],
    );
  }

  Widget _buildEmptyState(BuildContext context, ToGoLoaded state) {
    if (state.togos.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.place,
              size: 64,
              color: AppColors.textSecondary.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No ToGo yet',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: AppColors.textSecondary,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'Add your first ToGo destination!',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.textSecondary,
                  ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () => _showCreateDialog(context),
              icon: const Icon(Icons.add),
              label: const Text('Add ToGo'),
            ),
          ],
        ),
      );
    } else {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.filter_list,
              size: 64,
              color: AppColors.textSecondary.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No places match your filters',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: AppColors.textSecondary,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'Try adjusting your filters to see more places.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.textSecondary,
                  ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                context.read<ToGoBloc>().add(
                      const ToGoFilterChanged(),
                    );
              },
              icon: const Icon(Icons.clear),
              label: const Text('Clear Filters'),
            ),
          ],
        ),
      );
    }
  }

  Widget _buildToGoList(List<ToGoData> togos) {
    return RefreshIndicator(
      onRefresh: () async {
        context.read<ToGoBloc>().add(
              ToGoRefreshRequested(spaceId: widget.spaceId),
            );
      },
      child: ListView.builder(
        padding: const EdgeInsets.fromLTRB(
          AppConstants.defaultPadding,
          4,
          AppConstants.defaultPadding,
          AppConstants.defaultPadding,
        ),
        itemCount: togos.length,
        itemBuilder: (context, index) {
          final togo = togos[index];
          return ToGoCard(
            togo: togo,
            spaceId: widget.spaceId,
            onTap: () => _showToGoDetails(context, togo),
            onEdit: () => _showEditDialog(context, togo),
            onDelete: () => _showDeleteConfirmation(context, togo),
          );
        },
      ),
    );
  }

  void _showCreateDialog(BuildContext context) {
    // Store the correct context that has access to ToGoBloc
    final togoBloc = context.read<ToGoBloc>();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (dialogContext) => CreateToGoDialog(
        spaceId: widget.spaceId,
        onCreated: (request) {
          togoBloc.add(
            ToGoCreateRequested(
              spaceId: widget.spaceId,
              request: request,
            ),
          );
        },
      ),
    );
  }

  void _showToGoDetails(BuildContext context, ToGoData togo) {
    // TODO: Implement ToGo details dialog/page
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('ToGo details coming soon!')),
    );
  }

  void _showEditDialog(BuildContext context, ToGoData togo) {
    // Store the correct context that has access to ToGoBloc
    final togoBloc = context.read<ToGoBloc>();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (dialogContext) => CreateToGoDialog(
        spaceId: widget.spaceId,
        existingTogo: togo,
        onCreated: (request) {
          togoBloc.add(
            ToGoUpdateRequested(
              spaceId: widget.spaceId,
              togoId: togo.id,
              updates: request.toJson(),
            ),
          );
        },
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context, ToGoData togo) {
    // Store the correct context that has access to ToGoBloc
    final togoBloc = context.read<ToGoBloc>();

    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Delete Place'),
        content: Text('Are you sure you want to delete "${togo.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(dialogContext).pop();
              togoBloc.add(
                ToGoDeleteRequested(
                  spaceId: widget.spaceId,
                  togoId: togo.id,
                ),
              );
            },
            style: TextButton.styleFrom(foregroundColor: AppColors.error),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
