import '../../../../core/services/time_service.dart';
import '../../../../core/models/tag_model.dart';

class ToGoData {
  final String id;
  final String spaceId;
  final String name;
  final String? description;
  final String? address;
  final double? latitude;
  final double? longitude;
  final List<TagData> tags; // Tags from the unified Tag system
  final String creatorId;
  final DateTime createdAt;
  final String? originalLink;

  ToGoData({
    required this.id,
    required this.spaceId,
    required this.name,
    this.description,
    this.address,
    this.latitude,
    this.longitude,
    this.tags = const [],
    required this.creatorId,
    required this.createdAt,
    this.originalLink,
  });

  factory ToGoData.fromJson(Map<String, dynamic> json) {
    // Parse tags from the enriched response
    List<TagData> tags = [];
    if (json['tags'] != null && json['tags'] is List) {
      try {
        final tagsList = json['tags'] as List;
        tags = tagsList
            .map((tagJson) => TagData.fromJson(tagJson as Map<String, dynamic>))
            .toList();
      } catch (e) {
        // If parsing fails, use empty list
        tags = [];
      }
    }

    return ToGoData(
      id: json['id'],
      spaceId: json['spaceId'],
      name: json['name'],
      description: json['description'],
      address: json['address'],
      latitude: json['latitude']?.toDouble(),
      longitude: json['longitude']?.toDouble(),
      tags: tags,
      creatorId: json['creatorId'],
      createdAt: TimeService.instance.parseDateTime(json['createdAt']),
      originalLink: json['originalLink'],
    );
  }

  factory ToGoData.fromBackendJson(Map<String, dynamic> json) {
    // Parse tags from the enriched response
    List<TagData> tags = [];
    if (json['tags'] != null && json['tags'] is List) {
      try {
        final tagsList = json['tags'] as List;
        tags = tagsList
            .map((tagJson) => TagData.fromJson(tagJson as Map<String, dynamic>))
            .toList();
      } catch (e) {
        // If parsing fails, use empty list
        tags = [];
      }
    }

    return ToGoData(
      id: json['id'],
      spaceId: json['spaceId'],
      name: json['name'],
      description: json['description'],
      address: json['address'],
      latitude: json['latitude']?.toDouble(),
      longitude: json['longitude']?.toDouble(),
      tags: tags,
      creatorId: json['creatorId'],
      createdAt: TimeService.instance.parseDateTime(json['createdAt']),
      originalLink: json['originalLink'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'spaceId': spaceId,
      'name': name,
      'description': description,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'tags': tags.map((tag) => tag.toJson()).toList(),
      'creatorId': creatorId,
      'createdAt': createdAt.toIso8601String(),
      'originalLink': originalLink,
    };
  }

  ToGoData copyWith({
    String? name,
    String? description,
    String? address,
    double? latitude,
    double? longitude,
    List<TagData>? tags,
  }) {
    return ToGoData(
      id: id,
      spaceId: spaceId,
      name: name ?? this.name,
      description: description ?? this.description,
      address: address ?? this.address,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      tags: tags ?? this.tags,
      creatorId: creatorId,
      createdAt: createdAt,
      originalLink: originalLink,
    );
  }

  bool get hasLocation => latitude != null && longitude != null;

  String get displayLocation {
    if (address != null && address!.isNotEmpty) {
      return address!;
    }
    if (hasLocation) {
      return '${latitude!.toStringAsFixed(6)}, ${longitude!.toStringAsFixed(6)}';
    }
    return 'No location';
  }
}

// Create ToGo request model
class CreateToGoRequest {
  final String name;
  final String? description;
  final String? address;
  final double? latitude;
  final double? longitude;
  final String? originalLink;
  final List<String> tags; // Tags for the unified Tag system

  CreateToGoRequest({
    required this.name,
    this.description,
    this.address,
    this.latitude,
    this.longitude,
    this.originalLink,
    this.tags = const [],
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'originalLink': originalLink,
      'tags': tags,
    };
  }
}

// Note: ToGo tags are now managed through the unified Tag system
// Use TagSelector component for tag selection instead of predefined lists
