import 'package:dio/dio.dart';
import '../../../../core/network/api_client.dart';
import '../models/balance_model.dart';
import '../../../chat/data/models/split_model.dart';
import '../../../chat/data/models/message_model.dart';
import '../../../chat/domain/repositories/chat_repository.dart';

// Simple result wrapper for Balance operations
class BalanceResult<T> {
  final T? data;
  final String? error;
  final bool isSuccess;

  BalanceResult.success(this.data)
      : error = null,
        isSuccess = true;
  BalanceResult.error(this.error)
      : data = null,
        isSuccess = false;
}

class BalanceService {
  final ApiClient _apiClient;
  final Dio _dio;
  final ChatRepository _chatRepository;

  BalanceService(this._apiClient, this._dio, this._chatRepository);

  /// Get balance summary for a space
  Future<BalanceResult<BalanceSummary>> getSpaceBalance(String spaceId) async {
    try {
      // Use the new balance API
      final response = await _apiClient.getBalanceSummary(spaceId);

      if (!response.success || response.data == null) {
        return BalanceResult.error('Failed to get balance summary from API');
      }

      final data = response.data!;

      // Convert API response to BalanceSummary model
      final summary = BalanceSummary(
        spaceId: data['spaceId'] ?? spaceId,
        spaceName: data['spaceName'] ?? 'Space',
        spaceCurrency: data['spaceCurrency'] ?? 'USD',
        totalExpenses: (data['totalExpenses'] ?? 0.0).toDouble(),
        totalPaid: 0.0, // Will be calculated from userBalances
        totalOwed: 0.0, // Will be calculated from userBalances
        userBalances: (data['userBalances'] as List<dynamic>?)
                ?.map((item) => UserBalance.fromJson(item))
                .toList() ??
            [],
        recentSplits: [], // Will be populated separately if needed
        recentSettles: [], // Will be populated separately if needed
        lastUpdated:
            DateTime.tryParse(data['lastUpdated'] ?? '') ?? DateTime.now(),
      );

      return BalanceResult.success(summary);
    } catch (e) {
      return BalanceResult.error('Failed to get balance: $e');
    }
  }

  /// Get all splits for a space with filtering and sorting
  Future<BalanceResult<List<SplitData>>> getSpaceSplits(
    String spaceId, {
    BalanceFilter filter = BalanceFilter.all,
    BalanceSort sort = BalanceSort.dateNewest,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      // Get split records from balance API (debt records)
      final response = await _apiClient.getRecentSplits(spaceId, limit: limit);

      if (!response.success || response.data == null) {
        return BalanceResult.error('Failed to get splits from API');
      }

      final splitRecords = response.data as List<dynamic>;

      // Convert split message records to SplitData objects
      final splits = <SplitData>[];

      for (final recordData in splitRecords) {
        try {
          // Create SplitData from split message
          final participants = <SplitParticipant>[];
          final participantsList =
              recordData['participants'] as List<dynamic>? ?? [];

          for (final participantData in participantsList) {
            participants.add(SplitParticipant(
              userId: participantData['userId'],
              userName: participantData['userName'],
              amount: (participantData['amount'] as num).toDouble(),
            ));
          }

          final splitData = SplitData(
            id: recordData['id'], // Set the split ID from backend
            messageId: recordData['messageId'], // Set messageId separately
            title: recordData['title'] ?? 'Split Payment',
            description: recordData['description'] ?? '',
            category: recordData['category'] ?? 'general',
            totalAmount: (recordData['totalAmount'] as num).toDouble(),
            currency: recordData['currency'] ?? 'USD',
            paidByUserId: recordData['paidByUserId'],
            paidByUserName: recordData['paidByUserName'],
            splitType: _parseSplitType(recordData['splitType']),
            participants: participants,
            creatorId: recordData['creatorId'],
            createdAt: DateTime.parse(recordData['createdAt']),
          );
          splits.add(splitData);
        } catch (e) {
          // Skip invalid split records
          continue;
        }
      }

      // Apply filtering and sorting
      final filteredSplits = _applyFiltering(splits, filter);
      final sortedSplits = _applySorting(filteredSplits, sort);

      return BalanceResult.success(sortedSplits);
    } catch (e) {
      return BalanceResult.error('Failed to get splits: $e');
    }
  }

  /// Get expense categories statistics for a space
  Future<BalanceResult<List<ExpenseCategory>>> getExpenseCategories(
      String spaceId) async {
    try {
      // Get splits first
      final splitsResponse = await getSpaceSplits(spaceId);
      if (!splitsResponse.isSuccess) {
        return BalanceResult.error(splitsResponse.error!);
      }

      final splits = splitsResponse.data!;
      final categories = _calculateExpenseCategories(splits);
      return BalanceResult.success(categories);
    } catch (e) {
      return BalanceResult.error('Failed to get categories: $e');
    }
  }

  /// Mark a participant's payment as paid/unpaid
  Future<BalanceResult<bool>> updatePaymentStatus(
    String spaceId,
    String splitId,
    String participantId,
    bool isPaid,
  ) async {
    try {
      final response = await _apiClient.updateSplitPaymentStatus(
        splitId,
        participantId,
        isPaid,
      );

      if (!response.success) {
        return BalanceResult.error('Failed to update payment status');
      }

      return BalanceResult.success(true);
    } catch (e) {
      return BalanceResult.error('Failed to update payment: $e');
    }
  }

  /// Record a payment between two users with specific amount
  Future<BalanceResult<bool>> recordPayment(
    String spaceId,
    String fromUserId,
    String toUserId,
    double amount,
    String fromUserName,
    String toUserName, {
    String? currency,
  }) async {
    try {
      // Creating settle message for payment: $fromUserName -> $toUserName, amount: $amount

      // Create settle message instead of direct API call
      final settleMessage = CreateMessageRequest(
        content:
            'Payment recorded: ${currency ?? 'USD'} ${amount.toStringAsFixed(2)}',
        type: MessageType.settle,
        metadata: {
          'fromUserId': fromUserId,
          'fromUserName': fromUserName,
          'toUserId': toUserId,
          'toUserName': toUserName,
          'amount': amount,
          'currency': currency ?? 'USD',
          'description': '',
        },
      );

      // Sending settle message via WebSocket

      // Send settle message via WebSocket
      await _chatRepository.sendMessageViaWebSocket(spaceId, settleMessage);

      // Settle message sent successfully
      return BalanceResult.success(true);
    } catch (e) {
      // Failed to record payment: $e
      return BalanceResult.error('Failed to record payment: $e');
    }
  }

  /// Update a settle record
  Future<BalanceResult<bool>> updateSettle(
    String spaceId,
    String settleId,
    double amount,
    String description, {
    String? currency,
  }) async {
    try {
      final data = {
        'amount': amount,
        'description': description,
      };

      if (currency != null) {
        data['currency'] = currency;
      }

      final response = await _dio.put(
        '/balance/spaces/$spaceId/settles/$settleId',
        data: data,
      );

      if (response.statusCode == 200) {
        return BalanceResult.success(true);
      } else {
        return BalanceResult.error('Failed to update settle');
      }
    } catch (e) {
      return BalanceResult.error('Failed to update settle: $e');
    }
  }

  /// Delete a settle record using the dedicated settle endpoint
  /// This only soft deletes the settle record without affecting the chat message
  Future<BalanceResult<bool>> deleteSettle(
    String spaceId,
    String settleId, {
    String? messageId,
  }) async {
    try {
      // Always use the dedicated settle endpoint to avoid deleting chat messages
      final response =
          await _dio.delete('/balance/spaces/$spaceId/settles/$settleId');

      if (response.statusCode == 200) {
        // The backend handles soft deleting the settle record
        // and related balance records
        return BalanceResult.success(true);
      } else {
        return BalanceResult.error(
          response.data['message'] ?? 'Failed to delete settle',
        );
      }
    } catch (e) {
      if (e.toString().contains('404')) {
        return BalanceResult.error(
            'Settle not found or has already been deleted');
      } else if (e.toString().contains('403')) {
        return BalanceResult.error(
            'You do not have permission to delete this settle');
      } else if (e.toString().contains('Message has already been deleted')) {
        return BalanceResult.error('This settle has already been deleted');
      } else {
        return BalanceResult.error('Network error: Failed to delete settle');
      }
    }
  }

  /// Settle all debts between two users
  Future<BalanceResult<bool>> settleDebts(
    String spaceId,
    String fromUserId,
    String toUserId,
  ) async {
    try {
      final response = await _apiClient.settleDebts(
        spaceId,
        fromUserId,
        toUserId,
      );

      if (!response.success) {
        final errorMessage = response.message ??
            (response.errors?.isNotEmpty == true
                ? response.errors!.first
                : null) ??
            'Failed to settle debts';
        return BalanceResult.error(errorMessage);
      }

      return BalanceResult.success(true);
    } catch (e) {
      return BalanceResult.error('Failed to settle debts: $e');
    }
  }

  // Helper methods for data processing

  List<SplitData> _applyFiltering(
      List<SplitData> splits, BalanceFilter filter) {
    // For now, return all splits - filtering logic can be added later
    return splits;
  }

  List<SplitData> _applySorting(List<SplitData> splits, BalanceSort sort) {
    switch (sort) {
      case BalanceSort.dateNewest:
        splits.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case BalanceSort.dateOldest:
        splits.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        break;
      case BalanceSort.amountHighest:
        splits.sort((a, b) => b.totalAmount.compareTo(a.totalAmount));
        break;
      case BalanceSort.amountLowest:
        splits.sort((a, b) => a.totalAmount.compareTo(b.totalAmount));
        break;
      case BalanceSort.alphabetical:
        splits.sort((a, b) => a.title.compareTo(b.title));
        break;
    }
    return splits;
  }

  List<ExpenseCategory> _calculateExpenseCategories(List<SplitData> splits) {
    final Map<String, ExpenseCategory> categoryMap = {};
    double totalAmount = 0.0;

    // Calculate category totals
    for (final split in splits) {
      final category = split.category ?? 'Other';
      totalAmount += split.totalAmount;

      if (categoryMap.containsKey(category)) {
        final existing = categoryMap[category]!;
        categoryMap[category] = ExpenseCategory(
          name: existing.name,
          icon: existing.icon,
          color: existing.color,
          totalAmount: existing.totalAmount + split.totalAmount,
          splitCount: existing.splitCount + 1,
          percentage: 0.0, // Will be calculated below
        );
      } else {
        categoryMap[category] = ExpenseCategory(
          name: category,
          icon: _getCategoryIcon(category),
          color: _getCategoryColor(category),
          totalAmount: split.totalAmount,
          splitCount: 1,
          percentage: 0.0, // Will be calculated below
        );
      }
    }

    // Calculate percentages
    final categories = categoryMap.values.map((category) {
      final percentage =
          totalAmount > 0 ? (category.totalAmount / totalAmount) * 100 : 0.0;
      return ExpenseCategory(
        name: category.name,
        icon: category.icon,
        color: category.color,
        totalAmount: category.totalAmount,
        splitCount: category.splitCount,
        percentage: percentage,
      );
    }).toList();

    // Sort by total amount (highest first)
    categories.sort((a, b) => b.totalAmount.compareTo(a.totalAmount));
    return categories;
  }

  String _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'food':
      case 'drinks':
        return '🍽️';
      case 'transport':
      case 'car rental':
      case 'gas':
        return '🚗';
      case 'shopping':
        return '🛍️';
      case 'lodging':
        return '🏨';
      case 'flights':
        return '✈️';
      case 'activities':
      case 'sightseeing':
        return '🎯';
      case 'groceries':
        return '🛒';
      default:
        return '💰';
    }
  }

  int _getCategoryColor(String category) {
    switch (category.toLowerCase()) {
      case 'food':
      case 'drinks':
        return 0xFFE91E63;
      case 'transport':
      case 'car rental':
      case 'gas':
        return 0xFFFF9800;
      case 'shopping':
        return 0xFFFF5722;
      case 'lodging':
        return 0xFF9C27B0;
      case 'flights':
        return 0xFF2196F3;
      case 'activities':
      case 'sightseeing':
        return 0xFF4CAF50;
      case 'groceries':
        return 0xFF795548;
      default:
        return 0xFF607D8B;
    }
  }

  Future<BalanceResult<bool>> deleteSplit(
    String spaceId,
    String splitId,
  ) async {
    try {
      // Delete the split using the dedicated split endpoint
      final response = await _dio.delete('/splits/$splitId');

      if (response.statusCode == 200) {
        // The backend handles soft deleting the split record
        // and related balance records
        return BalanceResult.success(true);
      } else {
        return BalanceResult.error(
          response.data['message'] ?? 'Failed to delete split',
        );
      }
    } catch (e) {
      if (e.toString().contains('404')) {
        return BalanceResult.error(
            'Split not found or has already been deleted');
      } else if (e.toString().contains('403')) {
        return BalanceResult.error(
            'You do not have permission to delete this split');
      } else if (e.toString().contains('Message has already been deleted')) {
        return BalanceResult.error('This split has already been deleted');
      } else {
        return BalanceResult.error('Network error: Failed to delete split');
      }
    }
  }

  /// Get recent settle records for a space
  Future<BalanceResult<List<SettleRecord>>> getRecentSettles(
    String spaceId, {
    int limit = 10,
  }) async {
    try {
      final response = await _apiClient.getRecentSettles(spaceId, limit: limit);

      if (!response.success || response.data == null) {
        return BalanceResult.error('Failed to get recent settles from API');
      }

      final settles = (response.data as List<dynamic>)
          .map((item) => SettleRecord.fromJson(item))
          .toList();

      return BalanceResult.success(settles);
    } catch (e) {
      return BalanceResult.error('Failed to get recent settles: $e');
    }
  }

  /// Get space balance overview (all user pairs debt relationships)
  Future<BalanceResult<Map<String, dynamic>>> getSpaceBalanceOverview(
    String spaceId,
  ) async {
    try {
      final response = await _apiClient.getSpaceBalanceOverview(spaceId);

      if (!response.success || response.data == null) {
        return BalanceResult.error(
            'Failed to get space balance overview from API');
      }

      final overview = response.data as Map<String, dynamic>;

      return BalanceResult.success(overview);
    } catch (e) {
      return BalanceResult.error('Failed to get space balance overview: $e');
    }
  }

  Future<BalanceResult<String>> exportBalance(
    String spaceId,
    String format,
  ) async {
    try {
      // TODO: Implement actual API call when backend is ready
      // For now, return a mock download URL
      await Future.delayed(const Duration(milliseconds: 500));
      return BalanceResult.success('https://example.com/export.csv');
    } catch (e) {
      return BalanceResult.error('Failed to export balance: $e');
    }
  }

  /// Parse split type from string
  SplitType _parseSplitType(String? splitType) {
    switch (splitType?.toLowerCase()) {
      case 'equal':
        return SplitType.equal;
      case 'custom':
        return SplitType.custom;
      case 'percentage':
        return SplitType.percentage;
      default:
        return SplitType.equal;
    }
  }
}
