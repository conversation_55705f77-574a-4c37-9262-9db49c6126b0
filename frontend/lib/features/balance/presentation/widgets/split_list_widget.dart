import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../chat/data/models/split_model.dart';
import 'package:intl/intl.dart';
import '../../../../core/di/dependency_injection.dart';
import '../../../auth/domain/services/auth_service.dart';

class SplitListWidget extends StatelessWidget {
  final List<SplitData> splits;
  final bool isCompact;
  final Function(String splitId)? onSplitTap;
  final Function(SplitData split)? onEditSplit;
  final Function(String splitId)? onDeleteSplit;

  const SplitListWidget({
    super.key,
    required this.splits,
    this.isCompact = false,
    this.onSplitTap,
    this.onEditSplit,
    this.onDeleteSplit,
  });

  @override
  Widget build(BuildContext context) {
    if (splits.isEmpty) {
      return _buildEmptyState(context);
    }

    return ListView.builder(
      shrinkWrap: isCompact,
      physics: isCompact ? const NeverScrollableScrollPhysics() : null,
      padding: EdgeInsets.all(isCompact ? 0 : 16),
      itemCount: splits.length,
      itemBuilder: (context, index) {
        final split = splits[index];
        return _buildSplitCard(context, split);
      },
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.receipt_long,
              size: 48,
              color: AppColors.primary,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'No Splits Yet',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'Create your first split to start\ntracking shared expenses.',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textSecondary,
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildSplitCard(BuildContext context, SplitData split) {
    final categoryInfo = _getCategoryInfo(split.category ?? 'Other');

    return Card(
      margin: EdgeInsets.only(bottom: isCompact ? 8 : 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => onSplitTap?.call(split.id ?? ''), // Use split ID
        onLongPress: (onEditSplit != null || onDeleteSplit != null)
            ? () => _showSplitActions(context, split)
            : null,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Row
              Row(
                children: [
                  // Category Icon
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color:
                          Color(categoryInfo['color']).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      categoryInfo['icon'],
                      style: const TextStyle(fontSize: 16),
                    ),
                  ),

                  const SizedBox(width: 12),

                  // Title and Description
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          split.title,
                          style:
                              Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (split.description != null) ...[
                          const SizedBox(height: 2),
                          Text(
                            split.description!,
                            style:
                                Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: AppColors.textSecondary,
                                    ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ],
                    ),
                  ),

                  // Amount and Status
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        split.formattedAmount,
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.primary,
                                ),
                      ),
                      const SizedBox(height: 2),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Split Info Row
              Row(
                children: [
                  const Icon(
                    Icons.person,
                    size: 14,
                    color: AppColors.textSecondary,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Paid by ${_getPaidByDisplayName(split)}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.textSecondary,
                        ),
                  ),
                  const SizedBox(width: 16),
                  const Icon(
                    Icons.group,
                    size: 14,
                    color: AppColors.textSecondary,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${split.participants.length} participant${split.participants.length > 1 ? 's' : ''}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.textSecondary,
                        ),
                  ),
                  const Spacer(),
                  Text(
                    DateFormat('MMM dd, yyyy').format(split.createdAt),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.textSecondary,
                        ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showSplitActions(BuildContext context, SplitData split) {
    // Provide haptic feedback
    HapticFeedback.mediumImpact();

    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppColors.textSecondary.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),

            // Title
            Text(
              'Split Actions',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 20),

            // Edit option
            if (onEditSplit != null)
              ListTile(
                leading: const Icon(Icons.edit, color: AppColors.primary),
                title: const Text('Edit Split'),
                subtitle: const Text('Modify split details or participants'),
                onTap: () {
                  Navigator.pop(context);
                  onEditSplit!(split);
                },
              ),

            // Delete option
            if (onDeleteSplit != null)
              ListTile(
                leading: const Icon(Icons.delete, color: Colors.red),
                title: const Text('Delete Split',
                    style: TextStyle(color: Colors.red)),
                subtitle: const Text('Remove this split record'),
                onTap: () {
                  Navigator.pop(context);
                  _showDeleteConfirmation(context, split);
                },
              ),

            const SizedBox(height: 10),
          ],
        ),
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context, SplitData split) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Split'),
        content: Text(
          'Are you sure you want to delete this split?\n\n'
          '${split.title}\n'
          '${split.formattedAmount}\n'
          'Paid by ${split.paidByUserName}\n\n'
          'This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              if (split.id != null) {
                onDeleteSplit!(split.id!);
              } else {
                // Show error if no ID available
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Cannot delete split: ID not available'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Map<String, dynamic> _getCategoryInfo(String category) {
    final categories = {
      'Flights': {'icon': '✈️', 'color': 0xFF2196F3},
      'Lodging': {'icon': '🏨', 'color': 0xFF4CAF50},
      'Car rental': {'icon': '🚗', 'color': 0xFFFF9800},
      'Transit': {'icon': '🚌', 'color': 0xFF9C27B0},
      'Food': {'icon': '🍽️', 'color': 0xFFE91E63},
      'Drinks': {'icon': '🥤', 'color': 0xFF00BCD4},
      'Sightseeing': {'icon': '🏛️', 'color': 0xFF795548},
      'Activities': {'icon': '🎯', 'color': 0xFF607D8B},
      'Shopping': {'icon': '🛍️', 'color': 0xFFFF5722},
      'Gas': {'icon': '⛽', 'color': 0xFF3F51B5},
      'Groceries': {'icon': '🛒', 'color': 0xFF8BC34A},
      'Other': {'icon': '📋', 'color': 0xFF9E9E9E},
    };

    return categories[category] ?? categories['Other']!;
  }

  String _getPaidByDisplayName(SplitData split) {
    // Add (me) identifier for current user
    final authService = DependencyInjection.getIt<AuthService>();
    final currentUserId = authService.currentUser?.id ?? '';

    return split.paidByUserId == currentUserId
        ? '${split.paidByUserName}(me)'
        : split.paidByUserName;
  }
}
